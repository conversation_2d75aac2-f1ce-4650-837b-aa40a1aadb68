<div class="add-cash-modal-container">
  <!-- Modern Header with Gradient -->
  <div class="modal-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>account_balance_wallet</mat-icon>
      </div>
      <div class="header-text">
        <h2>Add Cash Inventory</h2>
        <p>Enhance your cash reserves with precision</p>
      </div>
    </div>
    <button mat-icon-button class="close-button" (click)="onCancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Main Content Area -->
  <div class="modal-content">
    <form #addCashForm="ngForm" class="add-cash-form">

      <!-- Step 1: Series Selection -->
      <div class="form-section series-selection">
        <div class="section-header">
          <mat-icon class="step-icon">category</mat-icon>
          <h3>Select Note Series</h3>
          <span class="pre-selected-badge" *ngIf="selectedSeries && data?.series">
            <mat-icon>check_circle</mat-icon>
            Pre-selected
          </span>
        </div>
        <div class="series-grid">
          <div *ngFor="let series of availableSeries"
               class="series-card"
               [class.selected]="selectedSeries === series.value"
               (click)="selectSeries(series.value)">
            <div class="series-icon">
              <mat-icon>{{ getSeriesIcon(series.value) }}</mat-icon>
            </div>
            <div class="series-info">
              <h4>{{ series.label }}</h4>
              <p>{{ getSeriesDescription(series.value) }}</p>
            </div>
            <div class="selection-indicator" *ngIf="selectedSeries === series.value">
              <mat-icon>check_circle</mat-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 2: Denomination Selection -->
      <div class="form-section denomination-selection" *ngIf="selectedSeries">
        <div class="section-header">
          <mat-icon class="step-icon">payments</mat-icon>
          <h3>Choose Denomination</h3>
          <span class="pre-selected-badge" *ngIf="selectedDenomination && data?.denomination">
            <mat-icon>check_circle</mat-icon>
            Pre-selected
          </span>
        </div>
        <div class="denomination-grid">
          <div *ngFor="let denom of availableDenominations"
               class="denomination-card"
               [class.selected]="selectedDenomination === denom.value"
               (click)="selectDenomination(denom.value)">
            <div class="denomination-content">
              <div class="denomination-icon">
                <mat-icon>payments</mat-icon>
              </div>
              <div class="denomination-info">
                <h4>{{ denom.label }}</h4>
                <p>{{ getDenominationDescription(denom.value) }}</p>
              </div>
            </div>
            <div class="selection-indicator" *ngIf="selectedDenomination === denom.value">
              <mat-icon>check_circle</mat-icon>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3: Quantity Input -->
      <div class="form-section quantity-section" *ngIf="selectedDenomination">
        <div class="section-header">
          <mat-icon class="step-icon">calculate</mat-icon>
          <h3>Specify Quantity</h3>
        </div>
        <div class="quantity-controls">
          <div class="quantity-card batches-card">
            <div class="card-header">
              <mat-icon>inventory_2</mat-icon>
              <h4>Batches</h4>
              <span class="helper-text">100 notes each</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(-1)" [disabled]="batches <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="batches"
                     name="batches"
                     min="0"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(1)">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-display">
              <span class="notes-count">{{ batches * 100 }} notes</span>
            </div>
          </div>

          <div class="quantity-card singles-card">
            <div class="card-header">
              <mat-icon>note</mat-icon>
              <h4>Singles</h4>
              <span class="helper-text">Individual notes</span>
            </div>
            <div class="input-container">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(-1)" [disabled]="singles <= 0">
                <mat-icon>remove</mat-icon>
              </button>
              <input type="number"
                     [(ngModel)]="singles"
                     name="singles"
                     min="0"
                     max="99"
                     class="quantity-input"
                     (input)="onQuantityChange()">
              <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(1)" [disabled]="singles >= 99">
                <mat-icon>add</mat-icon>
              </button>
            </div>
            <div class="quantity-display">
              <span class="notes-count">{{ singles }} notes</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 4: Reason Input -->
      <div class="form-section reason-section" *ngIf="totalQuantity > 0">
        <div class="section-header">
          <mat-icon class="step-icon">description</mat-icon>
          <h3>Add Reason (Optional)</h3>
        </div>
        <mat-form-field appearance="outline" class="reason-field">
          <mat-label>Reason for adding cash (optional)</mat-label>
          <textarea matInput
                    [(ngModel)]="reason"
                    name="reason"
                    rows="3"
                    placeholder="e.g., New stock delivery, Inventory replenishment, etc. (optional)"></textarea>
          <mat-icon matSuffix>edit_note</mat-icon>
        </mat-form-field>
      </div>

      <!-- Live Summary Display -->
      <div class="summary-display" *ngIf="selectedDenomination && totalQuantity > 0">
        <div class="summary-preview">
          <div class="preview-card">
            <div class="preview-header">
              <div class="preview-icon">
                <mat-icon>receipt_long</mat-icon>
              </div>
              <div class="preview-title">
                <h4>Adding to Inventory</h4>
                <p>{{ getSelectedSeriesLabel() }} • {{ getSelectedDenominationLabel() }}</p>
              </div>
            </div>

            <div class="preview-metrics">
              <div class="metric-item">
                <div class="metric-icon">
                  <mat-icon>inventory_2</mat-icon>
                </div>
                <div class="metric-details">
                  <span class="metric-value">{{ totalQuantity }}</span>
                  <span class="metric-label">Notes</span>
                </div>
              </div>

              <div class="metric-divider"></div>

              <div class="metric-item total-value">
                <div class="metric-icon">
                  <mat-icon>account_balance_wallet</mat-icon>
                </div>
                <div class="metric-details">
                  <span class="metric-value">{{ formatCurrency(selectedDenomination * totalQuantity) }}</span>
                  <span class="metric-label">Total Value</span>
                </div>
              </div>
            </div>

            <div class="breakdown-section" *ngIf="batches > 0 || singles > 0">
              <div class="breakdown-title">
                <mat-icon>analytics</mat-icon>
                <span>Breakdown</span>
              </div>
              <div class="breakdown-items">
                <div class="breakdown-item" *ngIf="batches > 0">
                  <span class="breakdown-label">{{ batches }} batch{{ batches !== 1 ? 'es' : '' }}</span>
                  <span class="breakdown-value">{{ batches * 100 }} notes</span>
                </div>
                <div class="breakdown-item" *ngIf="singles > 0">
                  <span class="breakdown-label">{{ singles }} single{{ singles !== 1 ? 's' : '' }}</span>
                  <span class="breakdown-value">{{ singles }} notes</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Action Buttons -->
  <div class="modal-actions">
    <button mat-stroked-button class="cancel-btn" (click)="onCancel()">
      <mat-icon>cancel</mat-icon>
      Cancel
    </button>
    <button mat-raised-button
            class="add-btn"
            [disabled]="!isFormValid()"
            (click)="onAddCash()">
      <mat-icon>add_circle</mat-icon>
      Add to Inventory
    </button>
  </div>
</div>
