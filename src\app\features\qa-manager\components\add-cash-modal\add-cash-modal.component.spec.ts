import { ComponentFixture, TestBed } from '@angular/core/testing';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

import { AddCashModalComponent, AddCashData } from './add-cash-modal.component';
import { NoteSeries, NoteDenomination } from '../../../shared/models/inventory.model';

describe('AddCashModalComponent', () => {
  let component: AddCashModalComponent;
  let fixture: ComponentFixture<AddCashModalComponent>;
  let mockDialogRef: jasmine.SpyObj<MatDialogRef<AddCashModalComponent>>;
  let mockSnackBar: jasmine.SpyObj<MatSnackBar>;

  const mockData: AddCashData = {
    series: NoteSeries.MANDELA,
    denomination: NoteDenomination.R100
  };

  beforeEach(async () => {
    mockDialogRef = jasmine.createSpyObj('MatDialogRef', ['close']);
    mockSnackBar = jasmine.createSpyObj('MatSnackBar', ['open']);

    await TestBed.configureTestingModule({
      imports: [
        AddCashModalComponent,
        NoopAnimationsModule
      ],
      providers: [
        { provide: MatDialogRef, useValue: mockDialogRef },
        { provide: MAT_DIALOG_DATA, useValue: mockData },
        { provide: MatSnackBar, useValue: mockSnackBar }
      ]
    }).compileComponents();

    fixture = TestBed.createComponent(AddCashModalComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should pre-populate series and denomination from data', () => {
    expect(component.selectedSeries).toBe(NoteSeries.MANDELA);
    expect(component.selectedDenomination).toBe(NoteDenomination.R100);
  });

  it('should calculate total quantity correctly', () => {
    component.batches = 2;
    component.singles = 50;
    expect(component.totalQuantity).toBe(250); // (2 * 100) + 50
  });

  it('should validate form correctly', () => {
    // Initially invalid (no quantity)
    expect(component.isFormValid()).toBeFalsy();

    // Add some quantity
    component.batches = 1;
    expect(component.isFormValid()).toBeTruthy();
  });

  it('should format currency correctly', () => {
    const formatted = component.formatCurrency(1000);
    expect(formatted).toContain('R');
    expect(formatted).toContain('1');
  });

  it('should adjust batches correctly', () => {
    component.batches = 5;
    component.adjustBatches(2);
    expect(component.batches).toBe(7);

    component.adjustBatches(-3);
    expect(component.batches).toBe(4);

    // Should not go below 0
    component.adjustBatches(-10);
    expect(component.batches).toBe(0);
  });

  it('should adjust singles correctly', () => {
    component.singles = 50;
    component.adjustSingles(10);
    expect(component.singles).toBe(60);

    component.adjustSingles(-20);
    expect(component.singles).toBe(40);

    // Should not go below 0
    component.adjustSingles(-100);
    expect(component.singles).toBe(0);

    // Should not go above 99
    component.singles = 95;
    component.adjustSingles(10);
    expect(component.singles).toBe(99);
  });

  it('should close dialog on cancel', () => {
    component.onCancel();
    expect(mockDialogRef.close).toHaveBeenCalledWith({ success: false });
  });

  it('should show error when form is invalid', () => {
    component.selectedSeries = null;
    component.onAddCash();
    expect(mockSnackBar.open).toHaveBeenCalledWith(
      'Please fill in all required fields',
      'Close',
      { duration: 3000 }
    );
  });

  it('should successfully add cash when form is valid', () => {
    component.selectedSeries = NoteSeries.MANDELA;
    component.selectedDenomination = NoteDenomination.R100;
    component.batches = 1;
    component.singles = 25;
    component.reason = 'Test addition';

    component.onAddCash();

    expect(mockDialogRef.close).toHaveBeenCalledWith({
      success: true,
      added: 125
    });
  });
});
