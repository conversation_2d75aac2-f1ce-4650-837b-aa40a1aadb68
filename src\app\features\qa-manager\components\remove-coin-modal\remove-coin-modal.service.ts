import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { RemoveCoinModalComponent, RemoveCoinData } from './remove-coin-modal.component';
import { CoinSeries, CoinDenomination } from '../../../../shared/models/inventory.model';

export interface RemoveCoinResult {
  success: boolean;
  removed?: number;
}

export interface RemoveCoinDialogData {
  seriesName?: string;
  denomination?: number;
  currentQuantity?: number;
}

@Injectable({
  providedIn: 'root'
})
export class RemoveCoinModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the Remove Coin modal with optional pre-configuration
   * @param data Optional data to pre-configure the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openRemoveCoinModal(data?: RemoveCoinDialogData): Observable<RemoveCoinResult | undefined> {
    // Convert string series name to enum if provided
    let series: CoinSeries | undefined;
    if (data?.seriesName) {
      series = data.seriesName as CoinSeries;
    }

    // Convert number denomination to enum if provided
    let denomination: CoinDenomination | undefined;
    if (data?.denomination) {
      denomination = data.denomination as CoinDenomination;
    }

    const modalData: RemoveCoinData = {
      series,
      denomination,
      currentQuantity: data?.currentQuantity
    };

    const dialogRef: MatDialogRef<RemoveCoinModalComponent, RemoveCoinResult> = this.dialog.open(
      RemoveCoinModalComponent,
      {
        width: '1200px',
        maxWidth: '98vw',
        data: modalData,
        disableClose: false,
        autoFocus: false,
        restoreFocus: true,
        panelClass: ['remove-coin-modal-panel', 'modern-modal-panel'],
        backdropClass: ['remove-coin-modal-backdrop', 'blur-backdrop'],
        hasBackdrop: true,
        closeOnNavigation: true,
        enterAnimationDuration: '400ms',
        exitAnimationDuration: '300ms',
        position: {
          top: '5vh'
        }
      }
    );

    // Handle backdrop click to close
    dialogRef.backdropClick().subscribe(() => {
      dialogRef.close({ success: false });
    });

    return dialogRef.afterClosed();
  }

  /**
   * Opens the Remove Coin modal with pre-selected series and denomination
   * @param series The series to pre-select
   * @param denomination The denomination to pre-select
   * @param currentQuantity The current quantity available for removal
   * @returns Observable that emits the result when the modal is closed
   */
  openRemoveCoinModalForDenomination(
    series: CoinSeries,
    denomination: CoinDenomination,
    currentQuantity?: number
  ): Observable<RemoveCoinResult | undefined> {
    return this.openRemoveCoinModal({
      seriesName: series,
      denomination: denomination,
      currentQuantity: currentQuantity
    });
  }

  /**
   * Checks if any Remove Coin modal is currently open
   * @returns boolean indicating if a modal is open
   */
  isModalOpen(): boolean {
    return this.dialog.openDialogs.some(
      dialog => dialog.componentInstance instanceof RemoveCoinModalComponent
    );
  }

  /**
   * Closes all open Remove Coin modals
   */
  closeAllModals(): void {
    this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof RemoveCoinModalComponent)
      .forEach(dialog => dialog.close({ success: false }));
  }
}
