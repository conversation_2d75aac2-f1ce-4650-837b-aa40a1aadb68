// ===== ABSA INVENTORY OVERVIEW COMPONENT STYLES =====

// ===== RESPONSIVE BREAKPOINTS =====
$mobile-small: 320px;
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
$desktop-large: 1200px;
$desktop-xl: 1440px;

// ===== RESPONSIVE MIXINS =====
@mixin mobile-small {
  @media (max-width: #{$mobile-small - 1px}) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: #{$mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: #{$tablet - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $desktop) {
    @content;
  }
}

@mixin desktop-large {
  @media (min-width: $desktop-large) {
    @content;
  }
}

.absa-inventory-container {
  background: var(--absa-white);
  position: relative;
  margin-top: 2rem; // Add space from navbar

  @include tablet {
    margin-top: 1rem;
  }

  @include mobile {
    margin-top: 0.5rem;
    // Scale down everything on mobile for better fit
    transform: scale(0.9);
    transform-origin: top left;
    width: 111.11%; // Compensate for scale
  }

  @include mobile-small {
    transform: scale(0.8);
    width: 125%; // Compensate for scale
  }
}

// ===== HERO HEADER SECTION =====
.absa-hero-header {
  position: relative;
  z-index: 1;
  background: var(--absa-gradient-primary);
  color: var(--absa-white);
  margin: 0 2rem 2rem 2rem; // Match content margins exactly
  padding: 2rem;
  border-radius: 12px; // Add rounded corners
  box-shadow: var(--absa-shadow-lg);

  @include tablet {
    margin: 0 1rem 1.5rem 1rem;
    padding: 1.5rem;
    border-radius: 8px;
  }

  @include mobile {
    margin: 0 0.5rem 1rem 0.5rem;
    padding: 1rem;
    border-radius: 6px;
  }

  @include mobile-small {
    margin: 0 0.25rem 0.75rem 0.25rem;
    padding: 0.75rem;
  }

  .hero-content {
    display: flex;
    justify-content: center; // Center the content
    align-items: center;
    gap: 2rem;

    @include tablet {
      flex-direction: column;
      text-align: center;
      gap: 1.5rem;
    }

    @include mobile {
      gap: 1rem;
    }

    @include mobile-small {
      gap: 0.75rem;
    }
  }

  .hero-text {
    text-align: center; // Center the text content

    .hero-title {
      font-size: 2.5rem;
      font-weight: 700;
      margin: 0 0 0.5rem 0;
      display: flex;
      align-items: center;
      justify-content: center; // Center the title content
      gap: 1rem;
      letter-spacing: -0.02em;

      @include tablet {
        font-size: 2rem;
        flex-direction: column;
        gap: 0.5rem;
        margin-bottom: 0.375rem;
      }

      @include mobile {
        font-size: 1.75rem;
        gap: 0.375rem;
        margin-bottom: 0.25rem;
      }

      @include mobile-small {
        font-size: 1.5rem;
        gap: 0.25rem;
        margin-bottom: 0.125rem;
      }

      .hero-icon {
        font-size: 3rem;
        width: 3rem;
        height: 3rem;
        color: var(--absa-gold);

        @include tablet {
          font-size: 2.5rem;
          width: 2.5rem;
          height: 2.5rem;
        }

        @include mobile {
          font-size: 2rem;
          width: 2rem;
          height: 2rem;
        }

        @include mobile-small {
          font-size: 1.75rem;
          width: 1.75rem;
          height: 1.75rem;
        }
      }
    }

    .hero-subtitle {
      font-size: 1.125rem;
      opacity: 0.9;
      margin: 0;
      font-weight: 400;

      @include tablet {
        font-size: 1rem;
      }

      @include mobile {
        font-size: 0.875rem;
      }

      @include mobile-small {
        font-size: 0.75rem;
      }
    }
  }

  .hero-actions {
    .absa-fab-primary {
      background: var(--absa-gold);
      color: var(--absa-red);
      font-weight: 600;
      box-shadow: var(--absa-shadow-lg);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        background: var(--absa-gold-light);
        transform: translateY(-2px);
        box-shadow: var(--absa-shadow-xl);
      }

      mat-icon {
        margin-right: 0.5rem;
      }
    }
  }
}

// ===== MAIN CONTENT AREA =====
.absa-main-content {
  position: relative;
  z-index: 1;
  margin: 0 2rem 2rem 2rem; // Match header margins
  padding: 0 0 2rem;

  @include tablet {
    margin: 0 1rem 1.5rem 1rem;
    padding: 0 0 1.5rem;
  }

  @include mobile {
    margin: 0 0.5rem 1rem 0.5rem;
    padding: 0 0 1rem;
  }

  @include mobile-small {
    margin: 0 0.25rem 0.75rem 0.25rem;
    padding: 0 0 0.75rem;
  }

  @media (max-width: 768px) {
    margin: 0 1rem 2rem 1rem;
    padding: 0 0 2rem;
  }
}

// ===== DASHBOARD SECTION =====
.absa-dashboard-section {
  margin-bottom: 4rem;

  @include tablet {
    margin-bottom: 3rem;
  }

  @include mobile {
    margin-bottom: 2rem;
  }

  @include mobile-small {
    margin-bottom: 1.5rem;
  }

  .dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;

    @include desktop-large {
      grid-template-columns: repeat(4, 1fr);
      gap: 2rem;
    }

    @include desktop {
      grid-template-columns: repeat(3, 1fr);
      gap: 1.75rem;
    }

    @include tablet {
      grid-template-columns: repeat(2, 1fr);
      gap: 1.25rem;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    @include mobile {
      grid-template-columns: 1fr;
      gap: 1rem;
    }

    @include mobile-small {
      gap: 0.75rem;
    }
  }
}

// ===== METRIC CARDS =====
.absa-metric-card {
  background: var(--absa-white);
  border-radius: 20px;
  padding: 1.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--absa-shadow-md);
  border: 1px solid var(--absa-gray-200);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;

  @include tablet {
    padding: 1.25rem;
    border-radius: 16px;
  }

  @include mobile {
    padding: 1rem;
    border-radius: 12px;
  }

  @include mobile-small {
    padding: 0.75rem;
    border-radius: 8px;
  }

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--absa-shadow-xl);
    border-color: var(--absa-red-200);

    @include mobile {
      transform: translateY(-2px);
    }

    @include mobile-small {
      transform: none;
    }
  }

  &:focus {
    outline: 2px solid var(--absa-red);
    outline-offset: 2px;
  }

  .card-decoration {
    position: absolute;
    top: -50px;
    right: -50px;
    width: 120px;
    height: 120px;
    border-radius: 50%;
    opacity: 0.1;
    transition: all 0.3s ease;

    @include tablet {
      width: 100px;
      height: 100px;
      top: -40px;
      right: -40px;
    }

    @include mobile {
      width: 80px;
      height: 80px;
      top: -30px;
      right: -30px;
    }

    @include mobile-small {
      width: 60px;
      height: 60px;
      top: -20px;
      right: -20px;
    }
  }

  .card-content {
    position: relative;
    z-index: 2;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;

    @include tablet {
      margin-bottom: 1.25rem;
    }

    @include mobile {
      margin-bottom: 1rem;
    }

    @include mobile-small {
      margin-bottom: 0.75rem;
    }

    .metric-icon {
      width: 60px;
      height: 60px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      box-shadow: var(--absa-shadow-sm);

      @include tablet {
        width: 50px;
        height: 50px;
        border-radius: 12px;
      }

      @include mobile {
        width: 40px;
        height: 40px;
        border-radius: 10px;
      }

      @include mobile-small {
        width: 32px;
        height: 32px;
        border-radius: 8px;
      }

      mat-icon {
        font-size: 1.75rem;
        width: 1.75rem;
        height: 1.75rem;
        color: var(--absa-white);

        @include tablet {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }

        @include mobile {
          font-size: 1.25rem;
          width: 1.25rem;
          height: 1.25rem;
        }

        @include mobile-small {
          font-size: 1rem;
          width: 1rem;
          height: 1rem;
        }
      }
    }

    .metric-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      padding: 0.375rem 0.75rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      @include tablet {
        padding: 0.25rem 0.5rem;
        font-size: 0.625rem;
        border-radius: 16px;
        gap: 0.125rem;
      }

      @include mobile {
        padding: 0.125rem 0.375rem;
        font-size: 0.5rem;
        border-radius: 12px;
      }

      @include mobile-small {
        padding: 0.125rem 0.25rem;
        font-size: 0.375rem;
        border-radius: 8px;
      }

      &.positive {
        background: rgba(52, 168, 83, 0.1);
        color: var(--absa-success);
      }

      &.neutral {
        background: var(--absa-gray-100);
        color: var(--absa-gray-600);
      }

      &.warning {
        background: rgba(251, 188, 4, 0.1);
        color: var(--absa-warning);
      }

      mat-icon {
        font-size: 0.875rem;
        width: 0.875rem;
        height: 0.875rem;

        @include tablet {
          font-size: 0.75rem;
          width: 0.75rem;
          height: 0.75rem;
        }

        @include mobile {
          font-size: 0.625rem;
          width: 0.625rem;
          height: 0.625rem;
        }

        @include mobile-small {
          font-size: 0.5rem;
          width: 0.5rem;
          height: 0.5rem;
        }
      }
    }
  }

  .card-body {
    .metric-value {
      font-size: 2.25rem;
      font-weight: 700;
      color: var(--absa-gray-900);
      line-height: 1;
      margin-bottom: 0.5rem;
      letter-spacing: -0.02em;

      @include tablet {
        font-size: 1.875rem;
        margin-bottom: 0.375rem;
      }

      @include mobile {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
      }

      @include mobile-small {
        font-size: 1.25rem;
        margin-bottom: 0.125rem;
      }
    }

    .metric-label {
      font-size: 1rem;
      font-weight: 600;
      color: var(--absa-gray-700);
      margin-bottom: 0.25rem;

      @include tablet {
        font-size: 0.875rem;
        margin-bottom: 0.125rem;
      }

      @include mobile {
        font-size: 0.75rem;
        margin-bottom: 0.125rem;
      }

      @include mobile-small {
        font-size: 0.625rem;
        margin-bottom: 0.0625rem;
      }
    }

    .metric-description {
      font-size: 0.875rem;
      color: var(--absa-gray-500);
      font-weight: 400;

      @include tablet {
        font-size: 0.75rem;
      }

      @include mobile {
        font-size: 0.625rem;
      }

      @include mobile-small {
        font-size: 0.5rem;
      }
    }
  }

  .card-progress {
    margin-top: 1rem;
    height: 4px;
    background: var(--absa-gray-200);
    border-radius: 2px;
    overflow: hidden;

    .progress-bar {
      height: 100%;
      background: var(--absa-gradient-primary);
      border-radius: 2px;
      transition: width 0.6s ease;

      &.warning {
        background: var(--absa-gradient-gold);
      }
    }
  }
}

// ===== CARD-SPECIFIC STYLING =====
.absa-metric-card {
  &.value-card {
    .card-decoration {
      background: var(--absa-success);
    }
    .metric-icon {
      background: var(--absa-gradient-primary);
    }
  }

  &.notes-card {
    .card-decoration {
      background: var(--absa-info);
    }
    .metric-icon {
      background: linear-gradient(135deg, var(--absa-info) 0%, var(--absa-info-light) 100%);
    }
  }

  &.alerts-card {
    .card-decoration {
      background: var(--absa-warning);
    }
    .metric-icon {
      background: var(--absa-gradient-gold);
    }

    &.critical {
      .card-decoration {
        background: var(--absa-error);
      }
      .metric-icon {
        background: linear-gradient(135deg, var(--absa-error) 0%, var(--absa-error-light) 100%);
      }
    }

    .alert-pulse {
      position: absolute;
      top: -4px;
      right: -4px;
      width: 12px;
      height: 12px;
      background: var(--absa-error);
      border-radius: 50%;
      animation: pulse 2s infinite;
    }
  }

  &.series-card {
    .card-decoration {
      background: var(--absa-red);
    }
    .metric-icon {
      background: var(--absa-gradient-primary);
    }
  }
}

// ===== INVENTORY SECTION =====
.absa-inventory-section {
  .inventory-container {
    background: var(--absa-white);
    border-radius: 24px;
    box-shadow: var(--absa-shadow-lg);
    overflow: hidden;
    border: 1px solid var(--absa-gray-200);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--absa-gradient-primary);
      z-index: 1;
    }
  }

  .inventory-content {
    padding: 2rem;

    @media (max-width: 768px) {
      padding: 1rem;
    }
  }
}

// Content area ready for redesign

// ===== SERIES DASHBOARD =====
.series-dashboard {
  margin-top: 2rem;

  .series-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
  }

  .series-stat-card {
    background: var(--absa-white);
    border: 1px solid var(--absa-gray-200);
    border-radius: 16px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    box-shadow: var(--absa-shadow-sm);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--absa-shadow-md);
      border-color: var(--absa-red-200);
    }

    .stat-icon-wrapper {
      width: 50px;
      height: 50px;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;

      mat-icon {
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
        color: var(--absa-white);
      }
    }

    &.notes-stat .stat-icon-wrapper {
      background: var(--absa-gradient-primary);
    }

    &.value-stat .stat-icon-wrapper {
      background: var(--absa-gradient-gold);
    }

    .stat-content {
      flex: 1;

      .stat-value {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--absa-gray-900);
        line-height: 1;
        margin-bottom: 0.25rem;
      }

      .stat-label {
        font-size: 0.875rem;
        color: var(--absa-gray-600);
        font-weight: 500;
        margin-bottom: 0.5rem;
      }

      .stat-trend {
        display: flex;
        align-items: center;
        gap: 0.25rem;
        font-size: 0.75rem;
        color: var(--absa-success);
        font-weight: 600;

        mat-icon {
          font-size: 0.875rem;
          width: 0.875rem;
          height: 0.875rem;
        }
      }
    }
  }
}

// ===== INVENTORY GRID =====
.absa-inventory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 1.5rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  @media (min-width: 1400px) {
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 2rem;
  }
}

// ===== INVENTORY CARDS =====
.inventory-card {
  background: var(--absa-white);
  border-radius: 20px;
  box-shadow: var(--absa-shadow-md);
  border: 1px solid var(--absa-gray-200);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;

  &:hover {
    transform: translateY(-4px);
    box-shadow: var(--absa-shadow-xl);
    border-color: var(--absa-red-200);
  }

  // Series-specific left border
  &.series-mandela {
    border-left: 4px solid var(--absa-success);
  }

  &.series-big_5 {
    border-left: 4px solid var(--absa-warning);
  }

  &.series-commemorative {
    border-left: 4px solid #9C27B0;
  }

  &.series-v6 {
    border-left: 4px solid var(--absa-info);
  }

  .card-header {
    padding: 1.5rem 1.5rem 1rem;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 1rem;

    .denomination-section {
      display: flex;
      align-items: center;
      gap: 1rem;
      flex: 1;

      .denomination-visual {
        position: relative;

        .note-preview {
          width: 60px;
          height: 40px;
          border-radius: 8px;
          overflow: hidden;
          position: relative;
          background: var(--absa-gray-100);
          border: 2px solid var(--absa-gray-300);

          .note-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }

          .note-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: var(--absa-red-100);
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            .note-icon {
              color: var(--absa-red);
              font-size: 1.25rem;
            }
          }

          &:hover .note-overlay {
            opacity: 1;
          }
        }

        .denomination-badge {
          position: absolute;
          bottom: -8px;
          right: -8px;
          background: var(--absa-gradient-primary);
          color: var(--absa-white);
          font-size: 0.75rem;
          font-weight: 600;
          padding: 0.25rem 0.5rem;
          border-radius: 12px;
          box-shadow: var(--absa-shadow-sm);
        }
      }

      .denomination-info {
        .denomination-title {
          font-size: 1.25rem;
          font-weight: 700;
          color: var(--absa-gray-900);
          margin: 0 0 0.25rem 0;
        }

        .denomination-series {
          font-size: 0.875rem;
          color: var(--absa-gray-600);
          margin: 0 0 0.5rem 0;
        }

        .denomination-value {
          font-size: 1rem;
          font-weight: 600;
          color: var(--absa-red);
        }
      }
    }

    .status-section {
      .status-chip {
        font-size: 0.75rem;
        font-weight: 600;
        padding: 0.375rem 0.75rem;
        border-radius: 16px;
        text-transform: uppercase;
        letter-spacing: 0.5px;

        &.status-in-stock {
          background: rgba(52, 168, 83, 0.1);
          color: var(--absa-success);
        }

        &.status-watch {
          background: rgba(251, 188, 4, 0.1);
          color: var(--absa-warning);
        }

        &.status-low {
          background: rgba(234, 67, 53, 0.1);
          color: var(--absa-error);
        }

        mat-icon {
          font-size: 0.875rem;
          width: 0.875rem;
          height: 0.875rem;
          margin-right: 0.25rem;
        }
      }
    }
  }

  .card-body {
    padding: 0 1.5rem 1rem;

    .metrics-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1.5rem;

      .metric-item {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 1rem;
        background: var(--absa-gray-50);
        border-radius: 12px;
        border: 1px solid var(--absa-gray-200);
        transition: all 0.3s ease;

        &:hover {
          background: var(--absa-red-50);
          border-color: var(--absa-red-200);
        }

        .metric-icon {
          width: 40px;
          height: 40px;
          border-radius: 10px;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;

          mat-icon {
            font-size: 1.25rem;
            width: 1.25rem;
            height: 1.25rem;
            color: var(--absa-white);
          }
        }

        &.quantity-metric .metric-icon {
          background: var(--absa-gradient-primary);
        }

        &.value-metric .metric-icon {
          background: var(--absa-gradient-gold);
        }

        .metric-content {
          .metric-value {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--absa-gray-900);
            line-height: 1;
            margin-bottom: 0.25rem;
          }

          .metric-label {
            font-size: 0.75rem;
            color: var(--absa-gray-600);
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
          }
        }
      }
    }

    .stock-level-section {
      .stock-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 0.5rem;

        .stock-label {
          font-size: 0.875rem;
          color: var(--absa-gray-600);
          font-weight: 500;
        }

        .stock-percentage {
          font-size: 0.875rem;
          color: var(--absa-red);
          font-weight: 600;
        }
      }

      .stock-progress-container {
        .absa-progress-bar {
          height: 8px;
          border-radius: 4px;
          background: var(--absa-gray-200);

          ::ng-deep {
            .mat-mdc-progress-bar-buffer {
              background: var(--absa-gray-200);
            }

            .mat-mdc-progress-bar-fill::after {
              background: var(--absa-gradient-primary);
            }

            &.mat-warn .mat-mdc-progress-bar-fill::after {
              background: var(--absa-gradient-gold);
            }
          }
        }
      }
    }
  }

  .card-actions {
    padding: 1rem 1.5rem 1.5rem;
    display: flex;
    gap: 0.75rem;
    border-top: 1px solid var(--absa-gray-200);

    .absa-action-btn {
      flex: 1;
      font-weight: 600;
      border-radius: 12px;
      padding: 0.75rem 1rem;
      transition: all 0.3s ease;

      &.primary {
        background: var(--absa-gradient-primary);
        color: var(--absa-white);
        border: none;
        box-shadow: var(--absa-shadow-sm);

        &:hover {
          transform: translateY(-1px);
          box-shadow: var(--absa-shadow-md);
        }
      }

      &.secondary {
        background: transparent;
        color: var(--absa-red);
        border: 1px solid var(--absa-red);

        &:hover {
          background: var(--absa-red-50);
        }
      }

      mat-icon {
        margin-right: 0.5rem;
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }
}

// ===== NO DATA STATE =====
.absa-no-data-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;

  .no-data-illustration {
    margin-bottom: 2rem;
    position: relative;

    .illustration-circle {
      width: 120px;
      height: 120px;
      border-radius: 50%;
      background: var(--absa-gradient-subtle);
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 1rem;
      position: relative;
      animation: float 3s ease-in-out infinite;

      .illustration-icon {
        font-size: 3rem;
        width: 3rem;
        height: 3rem;
        color: var(--absa-gray-400);
      }
    }

    .illustration-dots {
      display: flex;
      justify-content: center;
      gap: 0.5rem;

      .dot {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: var(--absa-red);
        animation: pulse 2s infinite;

        &:nth-child(2) {
          animation-delay: 0.2s;
        }

        &:nth-child(3) {
          animation-delay: 0.4s;
        }
      }
    }
  }

  .no-data-content {
    max-width: 400px;

    .no-data-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: var(--absa-gray-900);
      margin: 0 0 1rem 0;
    }

    .no-data-description {
      font-size: 1rem;
      color: var(--absa-gray-600);
      line-height: 1.5;
      margin: 0 0 2rem 0;
    }

    .absa-action-btn.large {
      padding: 1rem 2rem;
      font-size: 1rem;
      border-radius: 16px;

      mat-icon {
        margin-right: 0.75rem;
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }
}

// ===== FLOATING ACTION BUTTON =====
.absa-fab-container {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 1000;

  .absa-fab-primary {
    width: 64px;
    height: 64px;
    background: var(--absa-gradient-primary);
    color: var(--absa-white);
    box-shadow: var(--absa-shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

    &:hover {
      transform: scale(1.1) translateY(-2px);
      box-shadow: var(--absa-shadow-xl);
    }

    &:active {
      transform: scale(0.95);
    }

    mat-icon {
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }
  }

  @media (max-width: 768px) {
    bottom: 1rem;
    right: 1rem;

    .absa-fab-primary {
      width: 56px;
      height: 56px;

      mat-icon {
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }
  }
}

// ===== ANIMATIONS =====

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.8);
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// ===== RESPONSIVE DESIGN ENHANCEMENTS =====
@media (max-width: 1200px) {
  .absa-hero-header {
    .hero-content {
      padding: 0 1.5rem;
    }
  }

  .absa-main-content {
    margin: 0 1.5rem 2rem 1.5rem;
    padding: 0 0 2rem;
  }

  .absa-dashboard-section .dashboard-grid {
    gap: 1.25rem;
  }
}

@media (max-width: 768px) {

  .absa-hero-header {
    padding: 1.5rem 0;
    margin-bottom: 1.5rem;

    .hero-content {
      padding: 0 1rem;
    }

    .hero-text .hero-title {
      font-size: 1.75rem;
    }
  }

  .absa-main-content {
    margin: 0 1rem 1.5rem 1rem;
    padding: 0 0 1.5rem;
  }

  .series-dashboard .series-stats {
    gap: 1rem;
  }

  .inventory-card {
    .card-header {
      padding: 1rem;
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;

      .denomination-section {
        width: 100%;
      }

      .status-section {
        align-self: flex-end;
      }
    }

    .card-body {
      padding: 0 1rem 1rem;

      .metrics-grid {
        grid-template-columns: 1fr;
        gap: 0.75rem;
      }
    }

    .card-actions {
      padding: 1rem;
      flex-direction: column;

      .absa-action-btn {
        width: 100%;
      }
    }
  }
}

@media (max-width: 480px) {
  .absa-hero-header .hero-text .hero-title {
    font-size: 1.5rem;
  }

  .absa-dashboard-section .dashboard-grid {
    gap: 1rem;
  }

  .absa-metric-card {
    padding: 1rem;

    .card-header {
      margin-bottom: 1rem;

      .metric-icon {
        width: 50px;
        height: 50px;

        mat-icon {
          font-size: 1.5rem;
          width: 1.5rem;
          height: 1.5rem;
        }
      }
    }

    .card-body .metric-value {
      font-size: 1.75rem;
    }
  }

  .inventory-card .card-header .denomination-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
}

// ===== ACCESSIBILITY ENHANCEMENTS =====
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// ===== HIGH CONTRAST MODE =====
@media (prefers-contrast: high) {

  .absa-metric-card,
  .inventory-card {
    border-width: 2px;
    border-color: var(--absa-gray-900);
  }

  .absa-action-btn.primary {
    background: var(--absa-red) !important;
    border: 2px solid var(--absa-gray-900);
  }
}

// ===== PRINT STYLES =====
@media print {
  .absa-hero-header,
  .absa-fab-container,
  .card-actions {
    display: none !important;
  }

  .absa-inventory-container {
    background: white !important;
  }

  .absa-metric-card,
  .inventory-card {
    break-inside: avoid;
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }
}

// ===== DETAILED INVENTORY SECTION =====
.detailed-inventory-section {
  margin-top: 3rem;
  position: relative;
  z-index: 1;

  .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
    padding: 2rem 2.5rem;
    background: var(--absa-gradient-primary);
    border-radius: 16px 16px 0 0;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, transparent 0%, rgba(255, 255, 255, 0.1) 100%);
      pointer-events: none;
    }

    .section-title {
      font-size: 1.75rem;
      font-weight: 700;
      color: var(--absa-white);
      margin: 0;
      position: relative;
      z-index: 1;
    }

    .manage-btn {
      background: rgba(255, 255, 255, 0.15);
      color: var(--absa-white);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 12px;
      padding: 0.75rem 1.5rem;
      font-weight: 600;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
      backdrop-filter: blur(10px);

      &:hover {
        background: rgba(255, 255, 255, 0.25);
        border-color: rgba(255, 255, 255, 0.5);
        transform: translateY(-1px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      }

      mat-icon {
        margin-right: 0.5rem;
        font-size: 1rem;
        width: 1rem;
        height: 1rem;
      }
    }
  }

  .series-tabs-container {
    background: var(--absa-white);
    border-radius: 0 0 16px 16px;
    box-shadow: var(--absa-shadow-lg);
    overflow: hidden;
    border: 1px solid var(--absa-gray-200);
    border-top: none;

    ::ng-deep {
      .mat-mdc-tab-group {
        .mat-mdc-tab-header {
          border-bottom: 1px solid var(--absa-gray-200);
          background: var(--absa-gray-50);

          .mat-mdc-tab-label-container {
            .mat-mdc-tab {
              min-width: 140px;
              padding: 0 1.5rem;
              height: 60px;
              font-weight: 600;
              color: var(--absa-gray-600);
              transition: all 0.3s ease;
              position: relative;

              &:hover {
                background: rgba(196, 30, 58, 0.05);
                color: var(--absa-red);
              }

              &.mdc-tab--active {
                color: var(--absa-red);
                background: var(--absa-white);

                &::before {
                  content: '';
                  position: absolute;
                  bottom: 0;
                  left: 0;
                  right: 0;
                  height: 3px;
                  background: var(--absa-gradient-primary);
                }
              }

              .mdc-tab__content {
                .mdc-tab__text-label {
                  font-size: 0.875rem;
                  font-weight: inherit;
                }
              }
            }
          }
        }

        .mat-mdc-tab-body-wrapper {
          .mat-mdc-tab-body {
            .mat-mdc-tab-body-content {
              overflow: visible;
            }
          }
        }
      }
    }

    .tab-content {
      padding: 2.5rem;
      min-height: 600px;

      .series-summary {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 1.5rem;
        margin-bottom: 2.5rem;

        .summary-card {
          display: flex;
          align-items: center;
          padding: 1.75rem;
          background: linear-gradient(135deg, var(--absa-gray-50) 0%, var(--absa-white) 100%);
          border-radius: 16px;
          border: 1px solid var(--absa-gray-200);
          box-shadow: var(--absa-shadow-sm);
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: var(--absa-gradient-primary);
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: var(--absa-shadow-md);
            border-color: var(--absa-red-200);
          }

          .summary-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 56px;
            height: 56px;
            background: var(--absa-gradient-primary);
            border-radius: 16px;
            margin-right: 1.25rem;
            flex-shrink: 0;
            box-shadow: var(--absa-shadow-sm);

            mat-icon {
              color: var(--absa-white);
              font-size: 1.5rem;
              width: 1.5rem;
              height: 1.5rem;
            }
          }

          .summary-content {
            flex: 1;

            .summary-value {
              font-size: 1.5rem;
              font-weight: 700;
              color: var(--absa-gray-900);
              margin: 0 0 0.25rem 0;
              line-height: 1.2;
            }

            .summary-label {
              font-size: 0.875rem;
              color: var(--absa-gray-600);
              margin: 0;
              font-weight: 500;
            }
          }

          &.value-card {
            &::before {
              background: var(--absa-gradient-gold);
            }

            .summary-icon {
              background: var(--absa-gradient-gold);
            }
          }
        }
      }

      .denominations-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr));
        gap: 1.5rem;

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
          gap: 1rem;
        }

        .denomination-card {
          background: var(--absa-white);
          border-radius: 20px;
          border: 1px solid var(--absa-gray-200);
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
          position: relative;
          box-shadow: var(--absa-shadow-sm);

          &:hover {
            transform: translateY(-4px);
            box-shadow: var(--absa-shadow-lg);
            border-color: var(--absa-red-200);
          }

          &.low-stock {
            border-color: var(--absa-warning);

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 4px;
              background: var(--absa-gradient-gold);
              z-index: 1;
            }
          }

          &.out-of-stock {
            border-color: var(--absa-error);
            background: rgba(239, 68, 68, 0.02);

            &::before {
              content: '';
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              height: 4px;
              background: linear-gradient(90deg, var(--absa-error) 0%, #dc2626 100%);
              z-index: 1;
            }

            &::after {
              content: 'OUT OF STOCK';
              position: absolute;
              top: 12px;
              right: 12px;
              background: var(--absa-error);
              color: white;
              font-size: 0.65rem;
              font-weight: 700;
              padding: 4px 8px;
              border-radius: 6px;
              text-transform: uppercase;
              letter-spacing: 0.5px;
              z-index: 2;
              box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
            }

            .card-header,
            .card-body {
              opacity: 0.7;
            }

            .add-cash-btn {
              background: var(--absa-error) !important;

              &:hover {
                background: #dc2626 !important;
              }
            }
          }

          .card-header {
            padding: 1.5rem 1.75rem 1.25rem;
            border-bottom: 1px solid var(--absa-gray-100);
            background: linear-gradient(135deg, var(--absa-white) 0%, var(--absa-gray-50) 100%);

            .denomination-info {
              display: flex;
              align-items: flex-start;

              .denomination-icon {
                display: flex;
                align-items: center;
                justify-content: center;
                width: 48px;
                height: 48px;
                background: var(--absa-gradient-primary);
                border-radius: 14px;
                margin-right: 1rem;
                flex-shrink: 0;
                box-shadow: var(--absa-shadow-sm);

                mat-icon {
                  color: var(--absa-white);
                  font-size: 1.25rem;
                  width: 1.25rem;
                  height: 1.25rem;
                }
              }

              .denomination-details {
                flex: 1;

                .denomination-title {
                  font-size: 1.375rem;
                  font-weight: 700;
                  color: var(--absa-gray-900);
                  margin: 0 0 0.25rem 0;
                  line-height: 1.2;
                }

                .denomination-series {
                  font-size: 0.875rem;
                  color: var(--absa-gray-600);
                  margin: 0 0 0.75rem 0;
                  font-weight: 500;
                }

                .denomination-status {
                  .status-badge {
                    display: inline-flex;
                    align-items: center;
                    padding: 0.375rem 0.75rem;
                    border-radius: 8px;
                    font-size: 0.75rem;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                    transition: all 0.2s ease;

                    &.status-good {
                      background: rgba(34, 197, 94, 0.1);
                      color: var(--absa-success);
                      border: 1px solid rgba(34, 197, 94, 0.2);
                    }

                    &.status-medium {
                      background: rgba(251, 191, 36, 0.1);
                      color: var(--absa-warning);
                      border: 1px solid rgba(251, 191, 36, 0.2);
                    }

                    &.status-low {
                      background: rgba(239, 68, 68, 0.1);
                      color: var(--absa-error);
                      border: 1px solid rgba(239, 68, 68, 0.2);
                    }

                    &.status-out-of-stock {
                      background: rgba(239, 68, 68, 0.15);
                      color: var(--absa-error);
                      border: 1px solid rgba(239, 68, 68, 0.3);
                      font-weight: 700;
                      animation: pulse 2s infinite;
                    }
                  }
                }
              }
            }
          }

          .card-body {
            padding: 1.5rem 1.75rem;

            .inventory-stats {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 1.25rem;
              margin-bottom: 1.5rem;

              .stat-item {
                display: flex;
                flex-direction: column;
                padding: 1.25rem;
                background: var(--absa-gray-50);
                border-radius: 12px;
                border: 1px solid var(--absa-gray-200);
                transition: all 0.3s ease;

                &:hover {
                  background: var(--absa-red-50);
                  border-color: var(--absa-red-200);
                  transform: translateY(-1px);
                }

                .stat-value {
                  font-size: 1.125rem;
                  font-weight: 700;
                  color: var(--absa-gray-900);
                  margin: 0 0 0.25rem 0;
                  line-height: 1.2;
                }

                .stat-label {
                  font-size: 0.75rem;
                  color: var(--absa-gray-600);
                  font-weight: 500;
                  text-transform: uppercase;
                  letter-spacing: 0.5px;
                  margin: 0;
                }
              }
            }

            .stock-level {
              .stock-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.75rem;

                .stock-label {
                  font-size: 0.875rem;
                  color: var(--absa-gray-600);
                  font-weight: 600;
                }
              }

              .stock-progress {
                height: 8px;
                border-radius: 4px;
                background: var(--absa-gray-200);
                overflow: hidden;

                ::ng-deep {
                  .mat-mdc-progress-bar-buffer {
                    background: var(--absa-gray-200);
                  }

                  .mat-mdc-progress-bar-fill::after {
                    background: var(--absa-gradient-primary);
                    border-radius: 4px;
                  }

                  &.mat-warn .mat-mdc-progress-bar-fill::after {
                    background: var(--absa-gradient-gold);
                  }
                }

                &.out-of-stock-progress {
                  background: rgba(239, 68, 68, 0.1);
                  border: 1px solid rgba(239, 68, 68, 0.2);

                  ::ng-deep {
                    .mat-mdc-progress-bar-buffer {
                      background: rgba(239, 68, 68, 0.1);
                    }

                    .mat-mdc-progress-bar-fill::after {
                      background: var(--absa-error);
                    }
                  }
                }
              }
            }
          }

          .card-actions {
            padding: 1.25rem 1.75rem 1.5rem;
            border-top: 1px solid var(--absa-gray-100);
            background: var(--absa-gray-50);

            .action-buttons {
              display: grid;
              grid-template-columns: 1fr 1fr;
              gap: 0.75rem;
            }

            .add-cash-btn {
              background: var(--absa-gradient-primary);
              color: var(--absa-white);
              border: none;
              border-radius: 12px;
              padding: 0.875rem 1rem;
              font-weight: 600;
              font-size: 0.8rem;
              transition: all 0.3s ease;
              box-shadow: var(--absa-shadow-sm);

              &:hover {
                transform: translateY(-1px);
                box-shadow: var(--absa-shadow-md);
              }

              &:active {
                transform: translateY(0);
              }

              mat-icon {
                margin-right: 0.5rem;
                font-size: 1rem;
                width: 1rem;
                height: 1rem;
              }
            }

            .remove-cash-btn {
              background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
              color: var(--absa-white);
              border: none;
              border-radius: 12px;
              padding: 0.875rem 1rem;
              font-weight: 600;
              font-size: 0.8rem;
              transition: all 0.3s ease;
              box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);

              &:hover {
                transform: translateY(-1px);
                box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
              }

              &:active {
                transform: translateY(0);
              }

              mat-icon {
                margin-right: 0.5rem;
                font-size: 1rem;
                width: 1rem;
                height: 1rem;
              }
            }
          }
        }
      }
    }
  }
}

// ===== RESPONSIVE DESIGN FOR DETAILED INVENTORY =====
@media (max-width: 1200px) {
  .detailed-inventory-section {
    .section-header {
      padding: 1.5rem 2rem;
      flex-direction: column;
      gap: 1rem;
      text-align: center;

      .section-title {
        font-size: 1.5rem;
      }
    }

    .series-tabs-container .tab-content {
      padding: 2rem;

      .series-summary {
        gap: 1rem;
      }

      .denominations-grid {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      }
    }
  }
}

@media (max-width: 768px) {
  .detailed-inventory-section {
    margin-top: 2rem;

    .section-header {
      padding: 1.25rem 1.5rem;
      border-radius: 12px 12px 0 0;

      .section-title {
        font-size: 1.25rem;
      }

      .manage-btn {
        padding: 0.625rem 1rem;
        font-size: 0.8rem;

        mat-icon {
          font-size: 0.875rem;
          width: 0.875rem;
          height: 0.875rem;
        }
      }
    }

    .series-tabs-container {
      border-radius: 0 0 12px 12px;

      ::ng-deep .mat-mdc-tab-group .mat-mdc-tab-header .mat-mdc-tab-label-container .mat-mdc-tab {
        min-width: 100px;
        padding: 0 1rem;
        height: 50px;

        .mdc-tab__content .mdc-tab__text-label {
          font-size: 0.8rem;
        }
      }

      .tab-content {
        padding: 1.5rem;

        .series-summary {
          grid-template-columns: 1fr;
          gap: 1rem;
          margin-bottom: 1.5rem;

          .summary-card {
            padding: 1.25rem;

            .summary-icon {
              width: 48px;
              height: 48px;
              margin-right: 1rem;

              mat-icon {
                font-size: 1.25rem;
                width: 1.25rem;
                height: 1.25rem;
              }
            }

            .summary-content .summary-value {
              font-size: 1.25rem;
            }
          }
        }

        .denominations-grid {
          .denomination-card {
            .card-header {
              padding: 1.25rem;

              .denomination-info {
                .denomination-icon {
                  width: 40px;
                  height: 40px;

                  mat-icon {
                    font-size: 1rem;
                    width: 1rem;
                    height: 1rem;
                  }
                }

                .denomination-details .denomination-title {
                  font-size: 1.25rem;
                }
              }
            }

            .card-body {
              padding: 1.25rem;

              .inventory-stats {
                grid-template-columns: 1fr;
                gap: 1rem;
                margin-bottom: 1.25rem;

                .stat-item {
                  padding: 1rem;

                  .stat-value {
                    font-size: 1rem;
                  }
                }
              }
            }

            .card-actions {
              padding: 1rem 1.25rem;

              .add-cash-btn {
                padding: 0.75rem 1.25rem;
                font-size: 0.8rem;
              }
            }
          }
        }
      }
    }
  }
}

// ===== ENHANCED COIN-SPECIFIC STYLES =====
.coin-denominations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-top: 2rem;

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .coin-card {
    background: var(--absa-white);
    border-radius: 16px;
    border: 2px solid var(--absa-gray-200);
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
      border-color: var(--absa-red-300);
    }

    &.low-stock {
      border-color: var(--absa-warning);
      background: linear-gradient(135deg, rgba(251, 191, 36, 0.02) 0%, var(--absa-white) 100%);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--absa-warning) 0%, #f59e0b 100%);
        z-index: 1;
      }
    }

    &.out-of-stock {
      border-color: var(--absa-error);
      background: linear-gradient(135deg, rgba(239, 68, 68, 0.02) 0%, var(--absa-white) 100%);

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, var(--absa-error) 0%, #dc2626 100%);
        z-index: 1;
      }

      .coin-header,
      .coin-stats {
        opacity: 0.7;
      }
    }

    // Coin denomination color coding
    &[data-denomination="0.1"],
    &[data-denomination="0.2"] {
      .coin-badge {
        background: linear-gradient(135deg, #cd7f32 0%, #a0522d 100%);
        color: white;
      }
    }

    &[data-denomination="0.5"],
    &[data-denomination="1"] {
      .coin-badge {
        background: linear-gradient(135deg, #c0c0c0 0%, #a8a8a8 100%);
        color: #333;
      }
    }

    &[data-denomination="2"],
    &[data-denomination="5"] {
      .coin-badge {
        background: linear-gradient(135deg, #ffd700 0%, #e6c200 100%);
        color: #333;
      }
    }
  }

  .coin-header {
    padding: 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--absa-gray-100);
    background: linear-gradient(135deg, var(--absa-gray-50) 0%, var(--absa-white) 100%);

    .coin-badge {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      padding: 0.75rem 1rem;
      border-radius: 12px;
      font-weight: 700;
      font-size: 1.125rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;

      .coin-icon-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 32px;
        height: 32px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 8px;

        .coin-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
        }
      }

      .coin-value {
        font-size: 1.25rem;
        font-weight: 800;
        letter-spacing: -0.025em;
      }
    }

    .stock-indicator {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      padding: 0.5rem 0.75rem;
      border-radius: 8px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;

      mat-icon {
        font-size: 16px;
        width: 16px;
        height: 16px;
      }

      .status-text {
        @media (max-width: 480px) {
          display: none;
        }
      }

      &.status-normal {
        background: rgba(34, 197, 94, 0.1);
        color: var(--absa-success);
        border: 1px solid rgba(34, 197, 94, 0.2);
      }

      &.status-low {
        background: rgba(251, 191, 36, 0.1);
        color: var(--absa-warning);
        border: 1px solid rgba(251, 191, 36, 0.2);
      }

      &.status-out-of-stock {
        background: rgba(239, 68, 68, 0.1);
        color: var(--absa-error);
        border: 1px solid rgba(239, 68, 68, 0.2);
        animation: pulse 2s infinite;
      }
    }
  }

  .coin-stats {
    padding: 1.5rem;

    .stat-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
      margin-bottom: 1.25rem;

      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1rem;
        background: var(--absa-gray-50);
        border-radius: 12px;
        border: 1px solid var(--absa-gray-200);
        transition: all 0.3s ease;

        &:hover {
          background: var(--absa-red-50);
          border-color: var(--absa-red-200);
          transform: translateY(-1px);
        }

        mat-icon {
          font-size: 20px;
          width: 20px;
          height: 20px;
          color: var(--absa-gray-600);
          margin-bottom: 0.5rem;
        }

        .stat-value {
          font-size: 1.125rem;
          font-weight: 700;
          color: var(--absa-gray-900);
          margin-bottom: 0.25rem;
          line-height: 1;
        }

        .stat-label {
          font-size: 0.6875rem;
          color: var(--absa-gray-600);
          font-weight: 500;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          text-align: center;
          line-height: 1.2;
        }

        &.primary {
          mat-icon {
            color: var(--absa-red);
          }
        }

        &.secondary {
          mat-icon {
            color: var(--absa-gray-700);
          }
        }
      }
    }

    .total-value {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;
      padding: 1.25rem;
      background: linear-gradient(135deg, var(--absa-red-50) 0%, var(--absa-red-25) 100%);
      border: 1px solid var(--absa-red-200);
      border-radius: 12px;
      transition: all 0.3s ease;

      &:hover {
        background: linear-gradient(135deg, var(--absa-red-100) 0%, var(--absa-red-50) 100%);
        border-color: var(--absa-red-300);
      }

      mat-icon {
        font-size: 20px;
        width: 20px;
        height: 20px;
        color: var(--absa-red);
      }

      .value-amount {
        font-size: 1.25rem;
        font-weight: 800;
        color: var(--absa-red);
        letter-spacing: -0.025em;
      }

      .value-label {
        font-size: 0.75rem;
        color: var(--absa-red-700);
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
    }
  }

  .coin-actions {
    padding: 1rem 1.5rem 1.5rem;
    border-top: 1px solid var(--absa-gray-100);
    background: var(--absa-gray-50);

    .action-buttons {
      display: flex;
      gap: 12px;
    }

    .add-coins-btn, .remove-coins-btn {
      flex: 1;
      border: none;
      border-radius: 12px;
      padding: 0.875rem 1.5rem;
      font-weight: 600;
      font-size: 0.875rem;
      transition: all 0.3s ease;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      &:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      }

      mat-icon {
        margin-right: 0.5rem;
        font-size: 18px;
        width: 18px;
        height: 18px;
      }
    }

    .add-coins-btn {
      background: var(--absa-gradient-primary);
      color: var(--absa-white);
    }

    .remove-coins-btn {
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      color: var(--absa-white);

      &:hover {
        background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
      }
    }
  }
}

.tab-label {
  display: flex;
  align-items: center;
  gap: 8px;

  mat-icon {
    font-size: 18px;
  }
}

// Status indicators for coins
.status-normal {
  color: var(--absa-success);

  mat-icon {
    color: var(--absa-success);
  }
}

.status-low {
  color: var(--absa-warning);

  mat-icon {
    color: var(--absa-warning);
  }
}

.status-out-of-stock {
  color: var(--absa-red);

  mat-icon {
    color: var(--absa-red);
  }
}
