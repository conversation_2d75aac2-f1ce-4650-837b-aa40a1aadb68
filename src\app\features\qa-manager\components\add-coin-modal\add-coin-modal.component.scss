// Make the Material dialog container transparent
:host ::ng-deep .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}

// Main Container - Wider for better content visibility
.add-coin-modal-container {
  width: calc(100vw - 2rem);
  max-width: 1200px;
  margin: 0 1rem;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.85) 0%,
    rgba(241, 245, 249, 0.90) 50%,
    rgba(236, 242, 248, 0.92) 100%);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow:
    0 32px 64px rgba(15, 23, 42, 0.15),
    0 16px 32px rgba(15, 23, 42, 0.10),
    0 8px 16px rgba(15, 23, 42, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(226, 232, 240, 0.8);
  overflow: hidden;
  position: relative;
  animation: modalSlideIn 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

// Modal Header - Use ABSA red colors
.modal-header {
  background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
  padding: 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
                radial-gradient(circle at 70% 80%, rgba(255,255,255,0.08) 0%, transparent 50%);
    z-index: 1;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    z-index: 2;
    position: relative;

    .header-icon {
      width: 56px;
      height: 56px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);

      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        color: white;
      }
    }

    .header-text {
      h2 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 700;
        letter-spacing: -0.025em;
      }

      p {
        margin: 0.25rem 0 0;
        opacity: 0.9;
        font-size: 1rem;
        font-weight: 400;
      }
    }
  }

  .close-button {
    z-index: 2;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    mat-icon {
      font-size: 1.25rem;
    }
  }
}

// Modal Content
.modal-content {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(148, 163, 184, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(148, 163, 184, 0.5);
    }
  }
}

// Form Sections
.form-section {
  margin-bottom: 2rem;
  animation: slideInUp 0.4s ease-out;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }

  .section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;

    .step-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
      color: white;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.25rem;
    }

    h3 {
      color: var(--absa-gray-900);
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
    }
  }
}

// Series Grid
.series-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;

  .series-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(15, 23, 42, 0.12);
      border-color: rgba(220, 38, 38, 0.5);
    }

    &.selected {
      border-color: var(--absa-red);
      background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(185, 28, 60, 0.1) 100%);
      box-shadow: 0 8px 24px rgba(220, 38, 38, 0.2);

      .series-icon {
        background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
        color: white;
      }
    }

    .series-icon {
      width: 48px;
      height: 48px;
      background: rgba(148, 163, 184, 0.1);
      border: 1px solid rgba(148, 163, 184, 0.2);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;

      mat-icon {
        color: var(--absa-gray-600);
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }
    }

    .series-info {
      h4 {
        color: var(--absa-gray-900);
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 0.25rem;
      }

      p {
        color: var(--absa-gray-600);
        font-size: 0.875rem;
        margin: 0;
      }
    }
  }
}

// Denomination Grid
.denomination-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;

  .denomination-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    padding: 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(15, 23, 42, 0.12);
      border-color: rgba(220, 38, 38, 0.5);
    }

    &.selected {
      border-color: var(--absa-red);
      background: linear-gradient(135deg, rgba(220, 38, 38, 0.1) 0%, rgba(185, 28, 60, 0.1) 100%);
      box-shadow: 0 6px 20px rgba(220, 38, 38, 0.2);

      .denomination-icon {
        background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
        color: white;
      }
    }

    .denomination-icon {
      width: 40px;
      height: 40px;
      background: rgba(148, 163, 184, 0.1);
      border: 1px solid rgba(148, 163, 184, 0.2);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 0.75rem;
      transition: all 0.3s ease;

      mat-icon {
        color: var(--absa-gray-600);
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }

    .denomination-info {
      h4 {
        color: var(--absa-gray-900);
        font-size: 0.875rem;
        font-weight: 600;
        margin: 0 0 0.25rem;
      }

      p {
        color: var(--absa-gray-600);
        font-size: 0.75rem;
        margin: 0;
      }
    }
  }
}

// Quantity Controls
.quantity-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;

  .quantity-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 24px rgba(15, 23, 42, 0.12);
      border-color: rgba(220, 38, 38, 0.5);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;

      mat-icon {
        color: var(--absa-red);
        font-size: 1.25rem;
      }

      h4 {
        color: var(--absa-gray-900);
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
        flex: 1;
      }

      .helper-text {
        color: var(--absa-gray-600);
        font-size: 0.8rem;
        font-weight: 400;
      }
    }

    .input-container {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;

      .quantity-btn {
        width: 36px;
        height: 36px;
        min-width: 36px;
        background: rgba(148, 163, 184, 0.1);
        border: 1px solid rgba(148, 163, 184, 0.3);
        color: var(--absa-red);

        &:hover:not(:disabled) {
          background: var(--absa-red);
          color: white;
          border-color: var(--absa-red);
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        mat-icon {
          font-size: 1rem;
        }
      }

      .quantity-input {
        flex: 1;
        text-align: center;
        border: 2px solid rgba(226, 232, 240, 0.6);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 1rem;
        font-weight: 600;
        color: var(--absa-gray-900);
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: var(--absa-red);
          box-shadow: 0 0 0 3px rgba(145, 29, 47, 0.1);
        }
      }
    }

    .quantity-info {
      .batch-value,
      .coin-value {
        color: var(--absa-gray-600);
        font-size: 0.8rem;
        font-weight: 500;
      }
    }
  }
}

// Summary Section
.summary-section {
  .summary-card {
    background: rgba(145, 29, 47, 0.05);
    border: 2px solid rgba(145, 29, 47, 0.2);
    border-radius: 16px;
    padding: 1.5rem;

    .summary-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;

      mat-icon {
        color: var(--absa-red);
        font-size: 1.25rem;
      }

      h4 {
        color: var(--absa-red);
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
      }
    }

    .summary-content {
      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(145, 29, 47, 0.1);

        &:last-child {
          border-bottom: none;
        }

        .summary-label {
          color: var(--absa-gray-700);
          font-weight: 500;
        }

        .summary-value {
          color: var(--absa-gray-900);
          font-weight: 600;
        }
      }
    }

    .breakdown-section {
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid rgba(145, 29, 47, 0.2);

      .breakdown-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;

        mat-icon {
          color: var(--absa-red);
          font-size: 1rem;
        }

        span {
          color: var(--absa-red);
          font-weight: 600;
          font-size: 0.875rem;
        }
      }

      .breakdown-items {
        display: grid;
        gap: 0.5rem;

        .breakdown-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0.75rem;
          background: rgba(255, 255, 255, 0.5);
          border-radius: 8px;

          .breakdown-label {
            color: var(--absa-gray-700);
            font-size: 0.875rem;
            font-weight: 500;
          }

          .breakdown-value {
            color: var(--absa-gray-900);
            font-size: 0.875rem;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// Reason Field
.reason-field {
  width: 100%;

  .mat-mdc-form-field-wrapper {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 12px;
  }

  .mat-mdc-text-field-wrapper {
    background: transparent;
  }

  .mat-mdc-form-field-focus-overlay {
    background: rgba(145, 29, 47, 0.05);
  }

  .mat-mdc-form-field-outline {
    color: rgba(226, 232, 240, 0.6);
  }

  &.mat-focused .mat-mdc-form-field-outline-thick {
    color: var(--absa-red);
  }
}

// Action Buttons
.modal-actions {
  padding: 1.5rem 2rem;
  background: rgba(248, 250, 252, 0.8);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  display: flex;
  gap: 1rem;
  justify-content: flex-end;

  .cancel-btn {
    background: rgba(148, 163, 184, 0.1);
    color: var(--absa-gray-700);
    border: 1px solid rgba(148, 163, 184, 0.3);
    padding: 0.75rem 2rem;
    border-radius: 12px;
    font-weight: 500;
    font-size: 1rem;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(148, 163, 184, 0.2);
      transform: translateY(-1px);
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }

  .add-btn {
    background: linear-gradient(135deg, var(--absa-red) 0%, #B91C3C 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(227, 24, 55, 0.3);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(227, 24, 55, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

// Add the missing animation
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Responsive Design - Much smaller content for mobile
@media (max-width: 768px) {
  .add-coin-modal-container {
    width: 95vw;
    max-height: 90vh;
    transform: scale(0.9);
  }

  .modal-header {
    padding: 0.75rem;

    .header-content {
      gap: 0.5rem;

      .header-icon {
        width: 28px;
        height: 28px;
      }

      .header-text h2 {
        font-size: 1rem;
      }

      .header-text p {
        font-size: 0.625rem;
      }
    }
  }

  .modal-content {
    padding: 0.75rem;
  }

  .series-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }

  .series-card {
    padding: 0.5rem;
    border-radius: 6px;

    .series-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 0.25rem;

      mat-icon {
        font-size: 0.875rem;
      }
    }

    .series-info h4 {
      font-size: 0.625rem;
      margin-bottom: 0.125rem;
    }

    .series-info p {
      font-size: 0.5rem;
    }
  }

  .denomination-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.375rem;
  }

  .denomination-card {
    padding: 0.375rem;
    border-radius: 4px;

    .denomination-icon {
      width: 20px;
      height: 20px;
      margin-bottom: 0.25rem;

      mat-icon {
        font-size: 0.75rem;
      }
    }

    .denomination-info h4 {
      font-size: 0.5rem;
      margin-bottom: 0.125rem;
    }

    .denomination-info p {
      font-size: 0.375rem;
    }
  }

  .quantity-controls {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .quantity-input-group {
    .quantity-label {
      font-size: 0.625rem;
      margin-bottom: 0.25rem;
    }

    .quantity-input {
      padding: 0.375rem;
      font-size: 0.75rem;
      border-radius: 4px;
    }
  }

  .modal-actions {
    padding: 0.5rem 0.75rem;
    flex-direction: column;
    gap: 0.375rem;

    .cancel-btn,
    .add-btn {
      width: 100%;
      padding: 0.5rem;
      font-size: 0.625rem;
      border-radius: 4px;
    }
  }
}

@media (max-width: 480px) {
  .add-coin-modal-container {
    width: 100vw;
    max-height: 100vh;
    border-radius: 0;
    transform: scale(0.85);
  }

  .modal-header {
    padding: 0.5rem;

    .header-content {
      gap: 0.25rem;

      .header-icon {
        width: 24px;
        height: 24px;
      }

      .header-text h2 {
        font-size: 0.875rem;
      }

      .header-text p {
        font-size: 0.5rem;
      }
    }
  }

  .modal-content {
    padding: 0.5rem;
  }

  .series-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.25rem;
  }

  .denomination-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.25rem;
  }

  .quantity-controls {
    gap: 0.25rem;
  }

  .modal-actions {
    padding: 0.375rem 0.5rem;
    gap: 0.25rem;

    .cancel-btn,
    .add-btn {
      padding: 0.375rem;
      font-size: 0.5rem;
    }
  }
}

@media (max-width: 320px) {
  .modal-header {
    padding: 0.75rem;

    .header-content {
      .header-icon {
        width: 28px;
        height: 28px;
      }

      .header-text h2 {
        font-size: 1rem;
      }

      .header-text p {
        font-size: 0.625rem;
      }
    }
  }

  .modal-content {
    padding: 0.75rem;
  }

  .modal-actions {
    padding: 0.5rem 0.75rem;

    .cancel-btn,
    .add-btn {
      padding: 0.5rem;
      font-size: 0.625rem;
    }
  }
}
