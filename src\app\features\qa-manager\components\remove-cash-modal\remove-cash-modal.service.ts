import { Injectable } from '@angular/core';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { Observable } from 'rxjs';
import { RemoveCashModalComponent, RemoveCashData } from './remove-cash-modal.component';
import { NoteSeries, NoteDenomination } from '../../../../shared/models/inventory.model';

export interface RemoveCashResult {
  success: boolean;
  removed?: number;
}

export interface RemoveCashDialogData {
  seriesName?: string;
  denomination?: number;
  currentQuantity?: number;
}

@Injectable({
  providedIn: 'root'
})
export class RemoveCashModalService {

  constructor(private dialog: MatDialog) { }

  /**
   * Opens the Remove Cash modal with optional pre-configuration
   * @param data Optional data to pre-configure the modal
   * @returns Observable that emits the result when the modal is closed
   */
  openRemoveCashModal(data?: RemoveCashDialogData): Observable<RemoveCashResult | undefined> {
    // Convert string series name to enum if provided
    let series: NoteSeries | undefined;
    if (data?.seriesName) {
      series = data.seriesName as NoteSeries;
    }

    // Convert number denomination to enum if provided
    let denomination: NoteDenomination | undefined;
    if (data?.denomination) {
      denomination = data.denomination as NoteDenomination;
    }

    const modalData: RemoveCashData = {
      series,
      denomination,
      currentQuantity: data?.currentQuantity
    };

    const dialogRef: MatDialogRef<RemoveCashModalComponent, RemoveCashResult> = this.dialog.open(
      RemoveCashModalComponent,
      {
        width: '900px',
        maxWidth: '95vw',
        maxHeight: '90vh',
        data: modalData,
        disableClose: false,
        autoFocus: false,
        restoreFocus: true,
        panelClass: ['remove-cash-modal-panel', 'modern-modal-panel'],
        backdropClass: ['remove-cash-modal-backdrop', 'blur-backdrop'],
        hasBackdrop: true,
        closeOnNavigation: true,
        position: {
          top: '5vh'
        }
      }
    );

    // Handle backdrop click to close
    dialogRef.backdropClick().subscribe(() => {
      dialogRef.close({ success: false });
    });

    return dialogRef.afterClosed();
  }

  /**
   * Opens the Remove Cash modal with pre-selected series and denomination
   * @param series The series to pre-select
   * @param denomination The denomination to pre-select
   * @param currentQuantity The current quantity available for removal
   * @returns Observable that emits the result when the modal is closed
   */
  openRemoveCashModalForDenomination(
    series: NoteSeries,
    denomination: NoteDenomination,
    currentQuantity?: number
  ): Observable<RemoveCashResult | undefined> {
    return this.openRemoveCashModal({
      seriesName: series,
      denomination: denomination,
      currentQuantity: currentQuantity
    });
  }

  /**
   * Checks if any Remove Cash modal is currently open
   * @returns boolean indicating if a modal is open
   */
  isModalOpen(): boolean {
    return this.dialog.openDialogs.some(
      dialog => dialog.componentInstance instanceof RemoveCashModalComponent
    );
  }

  /**
   * Closes all open Remove Cash modals
   */
  closeAllModals(): void {
    this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof RemoveCashModalComponent)
      .forEach(dialog => dialog.close({ success: false }));
  }

  /**
   * Gets the count of currently open Remove Cash modals
   * @returns number of open modals
   */
  getOpenModalCount(): number {
    return this.dialog.openDialogs
      .filter(dialog => dialog.componentInstance instanceof RemoveCashModalComponent)
      .length;
  }
}
