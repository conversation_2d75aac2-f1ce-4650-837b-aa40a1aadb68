import { Component, Inject, OnInit, Renderer2, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';

import { NoteSeries, NoteDenomination, NOTE_SERIES_LABELS, DENOMINATION_LABELS } from '../../../../shared/models/inventory.model';

export interface RemoveCashData {
  series?: NoteSeries;
  denomination?: NoteDenomination;
  currentQuantity?: number;
}

// Mock services for now - these would be replaced with actual services
class MockInventoryService {
  removeCash(series: NoteSeries, denomination: NoteDenomination, quantity: number, reason: string): boolean {
    console.log('Mock InventoryService.removeCash called:', { series, denomination, quantity, reason });
    return true; // Always return success for demo
  }

  getCurrentQuantity(series: NoteSeries, denomination: NoteDenomination): number {
    // Mock current quantities for demo - using actual series IDs from inventory overview
    const mockQuantities: Record<string, Record<string, number>> = {
      'mandela': { '10': 2087, '20': 0, '50': 3079, '100': 3261, '200': 0 },
      'big5': { '10': 512, '20': 2543, '50': 815, '100': 0, '200': 1822 },
      'commemorative': { '10': 0, '20': 308, '50': 0, '100': 1208, '200': 205 },
      'v6': { '10': 1545, '20': 0, '50': 1233, '100': 2222, '200': 1512 }
    };
    return mockQuantities[series]?.[denomination.toString()] || 0;
  }
}

class MockSystemLogService {
  logManagerAction(action: string, description: string): void {
    console.log('Mock SystemLogService.logManagerAction called:', { action, description });
  }
}

@Component({
  selector: 'app-remove-cash-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatFormFieldModule
  ],
  templateUrl: './remove-cash-modal.component.html',
  styleUrls: ['./remove-cash-modal.component.scss']
})
export class RemoveCashModalComponent implements OnInit, AfterViewInit, OnDestroy {
  // Series and denomination options
  availableSeries = Object.values(NoteSeries);
  availableDenominations = [NoteDenomination.R10, NoteDenomination.R20, NoteDenomination.R50, NoteDenomination.R100, NoteDenomination.R200];

  // Form state
  selectedSeries: NoteSeries | null = null;
  selectedDenomination: NoteDenomination | null = null;
  batches: number = 0;
  singles: number = 0;
  reason: string = '';

  // Current inventory data
  currentQuantity: number = 0;
  currentBatches: number = 0;
  currentSingles: number = 0;

  // Calculated values
  totalQuantity: number = 0;
  remainingQuantity: number = 0;
  remainingBatches: number = 0;
  remainingSingles: number = 0;
  totalValue: number = 0;

  // Labels for display
  NOTE_SERIES_LABELS = NOTE_SERIES_LABELS;
  DENOMINATION_LABELS = DENOMINATION_LABELS;

  // Services
  private inventoryService = new MockInventoryService();
  private systemLogService = new MockSystemLogService();

  constructor(
    public dialogRef: MatDialogRef<RemoveCashModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: RemoveCashData,
    private snackBar: MatSnackBar,
    private renderer: Renderer2,
    private elementRef: ElementRef
  ) {
    // Pre-populate from dialog data
    if (data?.series) {
      this.selectedSeries = data.series;
    }
    if (data?.denomination) {
      this.selectedDenomination = data.denomination;
    }
    if (data?.currentQuantity !== undefined) {
      this.currentQuantity = data.currentQuantity;
      this.updateCurrentInventoryBreakdown();
    }
  }

  ngOnInit(): void {
    // If we have pre-selected series and denomination, load current inventory
    if (this.selectedSeries && this.selectedDenomination) {
      this.loadCurrentInventory();
    }
  }

  ngAfterViewInit(): void {
    // Apply backdrop blur effect
    setTimeout(() => {
      this.applyBackdropBlur();
    }, 100);
  }

  ngOnDestroy(): void {
    // Clean up any backdrop modifications
    this.removeBackdropBlur();
  }

  private applyBackdropBlur(): void {
    try {
      // Find all backdrop elements and apply subtle blur styles
      const backdrops = document.querySelectorAll('.cdk-overlay-backdrop');
      backdrops.forEach(backdrop => {
        if (backdrop instanceof HTMLElement) {
          this.renderer.setStyle(backdrop, 'backdrop-filter', 'blur(8px) saturate(1.1) brightness(0.9)');
          this.renderer.setStyle(backdrop, '-webkit-backdrop-filter', 'blur(8px) saturate(1.1) brightness(0.9)');
          this.renderer.setStyle(backdrop, 'background', 'rgba(15, 23, 42, 0.4)');
          this.renderer.setStyle(backdrop, 'transition', 'all 0.3s ease');
          this.renderer.addClass(backdrop, 'remove-cash-modal-backdrop');
        }
      });
    } catch (error) {
      console.warn('Could not apply backdrop blur:', error);
    }
  }

  private removeBackdropBlur(): void {
    try {
      const backdrops = document.querySelectorAll('.remove-cash-modal-backdrop');
      backdrops.forEach(backdrop => {
        if (backdrop instanceof HTMLElement) {
          this.renderer.removeClass(backdrop, 'remove-cash-modal-backdrop');
        }
      });
    } catch (error) {
      console.warn('Could not remove backdrop blur:', error);
    }
  }

  onSeriesChange(): void {
    this.selectedDenomination = null;
    this.resetQuantities();
    this.loadCurrentInventory();
  }

  onDenominationChange(): void {
    this.resetQuantities();
    this.loadCurrentInventory();
  }

  private loadCurrentInventory(): void {
    if (this.selectedSeries && this.selectedDenomination) {
      this.currentQuantity = this.inventoryService.getCurrentQuantity(this.selectedSeries, this.selectedDenomination);
      this.updateCurrentInventoryBreakdown();
      this.calculateValues();
    }
  }

  private updateCurrentInventoryBreakdown(): void {
    this.currentBatches = Math.floor(this.currentQuantity / 100);
    this.currentSingles = this.currentQuantity % 100;
  }

  private resetQuantities(): void {
    this.batches = 0;
    this.singles = 0;
    this.currentQuantity = 0;
    this.currentBatches = 0;
    this.currentSingles = 0;
    this.calculateValues();
  }

  adjustBatches(delta: number): void {
    const newBatches = Math.max(0, this.batches + delta);
    const maxBatches = Math.floor(this.currentQuantity / 100);
    this.batches = Math.min(newBatches, maxBatches);
    this.onQuantityChange();
  }

  adjustSingles(delta: number): void {
    const newSingles = Math.max(0, this.singles + delta);
    const maxSingles = this.currentQuantity - (this.batches * 100);
    this.singles = Math.min(newSingles, maxSingles);
    this.onQuantityChange();
  }

  onQuantityChange(): void {
    // Ensure we don't exceed available inventory
    const maxBatches = Math.floor(this.currentQuantity / 100);
    this.batches = Math.min(this.batches, maxBatches);

    const maxSingles = this.currentQuantity - (this.batches * 100);
    this.singles = Math.min(this.singles, maxSingles);

    this.calculateValues();
  }

  private calculateValues(): void {
    this.totalQuantity = (this.batches * 100) + this.singles;
    this.remainingQuantity = this.currentQuantity - this.totalQuantity;
    this.remainingBatches = Math.floor(this.remainingQuantity / 100);
    this.remainingSingles = this.remainingQuantity % 100;

    if (this.selectedDenomination) {
      this.totalValue = this.totalQuantity * this.selectedDenomination;
    }
  }

  isFormValid(): boolean {
    return !!(
      this.selectedSeries &&
      this.selectedDenomination &&
      this.totalQuantity > 0 &&
      this.totalQuantity <= this.currentQuantity
    );
  }

  getSelectedSeriesLabel(): string {
    return this.selectedSeries ? NOTE_SERIES_LABELS[this.selectedSeries] : '';
  }

  getSelectedDenominationLabel(): string {
    return this.selectedDenomination ? DENOMINATION_LABELS[this.selectedDenomination] : '';
  }

  onRemoveCash(): void {
    if (!this.isFormValid()) {
      this.snackBar.open('Please complete all required fields', 'Close', { duration: 3000 });
      return;
    }

    try {
      // Use provided reason or default to "Manual inventory removal"
      const reasonText = this.reason.trim() || 'Manual inventory removal';

      const success = this.inventoryService.removeCash(
        this.selectedSeries!,
        this.selectedDenomination!,
        this.totalQuantity,
        reasonText
      );

      if (success) {
        // Log the action
        const quantityDescription = this.batches > 0 && this.singles > 0
          ? `${this.batches} batches + ${this.singles} singles`
          : this.batches > 0
            ? `${this.batches} batches`
            : `${this.singles} singles`;

        this.systemLogService.logManagerAction(
          'REMOVE_CASH',
          `Removed ${quantityDescription} of ${this.getSelectedSeriesLabel()} ${this.getSelectedDenominationLabel()}`
        );

        this.snackBar.open(
          `Successfully removed ${quantityDescription} (${this.totalQuantity} notes) x ${DENOMINATION_LABELS[this.selectedDenomination!]}`,
          'Close',
          { duration: 4000 }
        );

        this.dialogRef.close({ success: true, removed: this.totalQuantity });
      } else {
        this.snackBar.open('Failed to remove cash inventory', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error removing cash:', error);
      this.snackBar.open('Error removing cash inventory', 'Close', { duration: 3000 });
    }
  }

  onCancel(): void {
    this.dialogRef.close({ success: false });
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }
}
