import { Component, Inject, On<PERSON>ni<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Renderer2 } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatIconModule } from '@angular/material/icon';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';

import { NoteSeries, NoteDenomination, NOTE_SERIES_LABELS, DENOMINATION_LABELS } from '../../../../shared/models/inventory.model';

export interface AddCashData {
  series?: NoteSeries;
  denomination?: NoteDenomination;
}

// Mock services for now - these would be replaced with actual services
class MockInventoryService {
  addCash(series: NoteSeries, denomination: NoteDenomination, quantity: number, reason: string): boolean {
    console.log('Mock InventoryService.addCash called:', { series, denomination, quantity, reason });
    return true; // Always return success for demo
  }
}

class MockSystemLogService {
  logManagerAction(action: string, description: string): void {
    console.log('Mock SystemLogService.logManagerAction called:', { action, description });
  }
}

@Component({
  selector: 'app-add-cash-modal',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatDialogModule,
    MatButtonModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatIconModule,
    MatSnackBarModule
  ],
  templateUrl: './add-cash-modal.component.html',
  styleUrls: ['./add-cash-modal.component.scss']
})
export class AddCashModalComponent implements OnInit, OnDestroy {
  selectedSeries: NoteSeries | null = null;
  selectedDenomination: NoteDenomination | null = null;
  batches: number = 0;
  singles: number = 0;
  reason: string = '';

  // Computed property for total quantity
  get totalQuantity(): number {
    return (this.batches * 100) + this.singles;
  }

  availableSeries = Object.values(NoteSeries).map(series => ({
    value: series,
    label: NOTE_SERIES_LABELS[series]
  }));

  availableDenominations = Object.values(NoteDenomination)
    .filter(d => typeof d === 'number')
    .map(denom => ({
      value: denom as NoteDenomination,
      label: DENOMINATION_LABELS[denom as NoteDenomination]
    }));

  private inventoryService = new MockInventoryService();
  private systemLogService = new MockSystemLogService();

  constructor(
    private dialogRef: MatDialogRef<AddCashModalComponent>,
    @Inject(MAT_DIALOG_DATA) public data: AddCashData,
    private snackBar: MatSnackBar,
    private renderer: Renderer2
  ) {
    // Pre-populate if data provided
    if (data?.series) {
      this.selectedSeries = data.series;
    }
    if (data?.denomination) {
      this.selectedDenomination = data.denomination;
    }
  }

  ngOnInit(): void {
    // Force apply backdrop blur styles programmatically
    setTimeout(() => {
      this.applyBackdropBlur();
    }, 100);
  }

  ngOnDestroy(): void {
    // Cleanup if needed
  }

  private applyBackdropBlur(): void {
    try {
      // Find all backdrop elements and apply subtle blur styles
      const backdrops = document.querySelectorAll('.cdk-overlay-backdrop');
      backdrops.forEach(backdrop => {
        if (backdrop instanceof HTMLElement) {
          this.renderer.setStyle(backdrop, 'backdrop-filter', 'blur(8px) saturate(1.1) brightness(0.9)');
          this.renderer.setStyle(backdrop, '-webkit-backdrop-filter', 'blur(8px) saturate(1.1) brightness(0.9)');
          this.renderer.setStyle(backdrop, 'background', 'rgba(15, 23, 42, 0.4)');
          this.renderer.setStyle(backdrop, 'transition', 'all 0.3s ease');
          this.renderer.addClass(backdrop, 'blur-backdrop-applied');
        }
      });
    } catch (error) {
      console.warn('Could not apply backdrop blur:', error);
    }
  }

  isFormValid(): boolean {
    return !!(this.selectedSeries &&
              this.selectedDenomination &&
              this.totalQuantity > 0);
  }

  selectSeries(series: NoteSeries): void {
    this.selectedSeries = series;
    // Reset denomination when series changes (unless pre-selected)
    if (!this.data?.denomination) {
      this.selectedDenomination = null;
    }
  }

  selectDenomination(denomination: NoteDenomination): void {
    this.selectedDenomination = denomination;
  }

  adjustBatches(delta: number): void {
    this.batches = Math.max(0, this.batches + delta);
    this.onQuantityChange();
  }

  adjustSingles(delta: number): void {
    this.singles = Math.max(0, Math.min(99, this.singles + delta));
    this.onQuantityChange();
  }

  onQuantityChange(): void {
    // Ensure singles don't exceed 99
    if (this.singles > 99) {
      this.singles = 99;
    }
    if (this.singles < 0) {
      this.singles = 0;
    }
    if (this.batches < 0) {
      this.batches = 0;
    }
  }

  getSeriesIcon(series: NoteSeries): string {
    const icons = {
      [NoteSeries.MANDELA]: 'account_balance',
      [NoteSeries.BIG_5]: 'nature',
      [NoteSeries.COMMEMORATIVE]: 'star',
      [NoteSeries.V6]: 'new_releases'
    };
    return icons[series] || 'category';
  }

  getSeriesDescription(series: NoteSeries): string {
    const descriptions = {
      [NoteSeries.MANDELA]: 'Standard circulation notes',
      [NoteSeries.BIG_5]: 'Wildlife themed series',
      [NoteSeries.COMMEMORATIVE]: 'Special edition notes',
      [NoteSeries.V6]: 'Latest series design'
    };
    return descriptions[series] || 'Note series';
  }

  getDenominationDescription(denomination: NoteDenomination): string {
    const descriptions = {
      [NoteDenomination.R10]: 'Ten Rand note',
      [NoteDenomination.R20]: 'Twenty Rand note',
      [NoteDenomination.R50]: 'Fifty Rand note',
      [NoteDenomination.R100]: 'One Hundred Rand note',
      [NoteDenomination.R200]: 'Two Hundred Rand note'
    };
    return descriptions[denomination] || 'Bank note';
  }

  getSelectedSeriesLabel(): string {
    return this.selectedSeries ? NOTE_SERIES_LABELS[this.selectedSeries] : '';
  }

  getSelectedDenominationLabel(): string {
    return this.selectedDenomination ? DENOMINATION_LABELS[this.selectedDenomination] : '';
  }

  onAddCash(): void {
    if (!this.isFormValid()) {
      this.snackBar.open('Please fill in all required fields', 'Close', { duration: 3000 });
      return;
    }

    try {
      // Use provided reason or default to "Manual inventory addition"
      const reasonText = this.reason.trim() || 'Manual inventory addition';

      const success = this.inventoryService.addCash(
        this.selectedSeries!,
        this.selectedDenomination!,
        this.totalQuantity,
        reasonText
      );

      if (success) {
        // Log the action
        const quantityDescription = this.batches > 0 && this.singles > 0
          ? `${this.batches} batches + ${this.singles} singles`
          : this.batches > 0
            ? `${this.batches} batches`
            : `${this.singles} singles`;

        this.systemLogService.logManagerAction(
          'ADD_CASH',
          `Added ${quantityDescription} of ${this.getSelectedSeriesLabel()} ${this.getSelectedDenominationLabel()}`
        );

        this.snackBar.open(
          `Successfully added ${quantityDescription} (${this.totalQuantity} notes) x ${DENOMINATION_LABELS[this.selectedDenomination!]}`,
          'Close',
          { duration: 4000 }
        );

        this.dialogRef.close({ success: true, added: this.totalQuantity });
      } else {
        this.snackBar.open('Failed to add cash inventory', 'Close', { duration: 3000 });
      }
    } catch (error) {
      console.error('Error adding cash:', error);
      this.snackBar.open('Error adding cash inventory', 'Close', { duration: 3000 });
    }
  }

  onCancel(): void {
    this.dialogRef.close({ success: false });
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-ZA', {
      style: 'currency',
      currency: 'ZAR'
    }).format(amount);
  }
}
