// Make the Material dialog container transparent
:host ::ng-deep .remove-coin-modal-panel .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}

:host ::ng-deep .modern-modal-panel .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}

// Main Container
.remove-coin-modal-container {
  width: 100%;
  max-width: 1200px;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 32px 64px rgba(0,0,0,0.12);
  overflow: hidden;
  animation: slideInUp 0.4s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Header Section
.modal-header {
  background: linear-gradient(135deg, #cc0000 0%, #990000 100%);
  color: white;
  padding: 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 20px;
    position: relative;
    z-index: 1;

    .header-icon {
      width: 56px;
      height: 56px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);

      mat-icon {
        font-size: 28px;
        width: 28px;
        height: 28px;
        color: white;
      }
    }

    .header-text {
      h2 {
        margin: 0;
        font-size: 28px;
        font-weight: 600;
        letter-spacing: -0.5px;
      }

      p {
        margin: 4px 0 0 0;
        font-size: 16px;
        opacity: 0.9;
        font-weight: 400;
      }
    }
  }

  .close-button {
    position: relative;
    z-index: 1;
    width: 48px;
    height: 48px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    mat-icon {
      color: white;
      font-size: 24px;
    }
  }
}

// Content Layout
.modal-content {
  padding: 40px;
}

.content-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 40px;
  align-items: start;
}

// Form Sections
.form-section {
  margin-bottom: 40px;

  .section-header {
    display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 24px;

    .step-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #cc0000 0%, #990000 100%);
      color: white;
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 20px;
    }

    h3 {
      margin: 0;
      font-size: 22px;
      font-weight: 600;
      color: #1e293b;
    }
  }
}

// Series Grid
.series-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.series-card {
  padding: 24px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 16px;

  &:hover {
    border-color: #cc0000;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(204, 0, 0, 0.15);
  }

  &.selected {
    border-color: #cc0000;
    background: linear-gradient(135deg, rgba(204, 0, 0, 0.05) 0%, rgba(153, 0, 0, 0.02) 100%);
    box-shadow: 0 8px 25px rgba(204, 0, 0, 0.15);
  }

  .series-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #cc0000 0%, #990000 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;

    mat-icon {
      color: white;
      font-size: 24px;
    }
  }

  .series-info {
    h4 {
      margin: 0 0 4px 0;
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #64748b;
    }
  }
}

// Denomination Grid
.denomination-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.denomination-card {
  padding: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  background: white;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;

  &:hover {
    border-color: #cc0000;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(204, 0, 0, 0.15);
  }

  &.selected {
    border-color: #cc0000;
    background: linear-gradient(135deg, rgba(204, 0, 0, 0.05) 0%, rgba(153, 0, 0, 0.02) 100%);
    box-shadow: 0 8px 25px rgba(204, 0, 0, 0.15);
  }

  .denomination-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #cc0000 0%, #990000 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 16px auto;

    mat-icon {
      color: white;
      font-size: 24px;
    }
  }

  .denomination-info {
    h4 {
      margin: 0 0 4px 0;
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
    }

    p {
      margin: 0;
      font-size: 12px;
      color: #64748b;
    }
  }
}

// Quantity Controls
.quantity-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.quantity-card {
  padding: 24px;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  background: white;

  .card-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;

    mat-icon {
      color: #cc0000;
      font-size: 20px;
    }

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #1e293b;
      flex: 1;
    }

    .helper-text {
      font-size: 12px;
      color: #64748b;
    }
  }

  .input-container {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;

    .quantity-btn {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      background: #f1f5f9;
      border: 1px solid #e2e8f0;

      &:hover:not(:disabled) {
        background: #cc0000;
        color: white;
      }

      &:disabled {
        opacity: 0.5;
      }
    }

    .quantity-input {
      flex: 1;
      padding: 12px 16px;
      border: 2px solid #e2e8f0;
      border-radius: 8px;
      font-size: 16px;
      text-align: center;
      font-weight: 600;

      &:focus {
        outline: none;
        border-color: #cc0000;
      }
    }
  }

  .quantity-info {
    display: flex;
    flex-direction: column;
    gap: 4px;

    span {
      font-size: 12px;
      color: #64748b;

      &.available {
        font-weight: 500;
      }

      &.value {
        color: #059669;
        font-weight: 600;
      }
    }
  }
}

// Reason Field
.reason-field {
  width: 100%;

  .mat-mdc-form-field-wrapper {
    .mat-mdc-form-field-flex {
      .mat-mdc-form-field-outline {
        .mat-mdc-form-field-outline-start,
        .mat-mdc-form-field-outline-notch,
        .mat-mdc-form-field-outline-end {
          border-color: #e2e8f0;
          border-width: 2px;
        }
      }

      &.mdc-text-field--focused {
        .mat-mdc-form-field-outline {
          .mat-mdc-form-field-outline-start,
          .mat-mdc-form-field-outline-notch,
          .mat-mdc-form-field-outline-end {
            border-color: #cc0000;
          }
        }
      }
    }
  }
}

// Right Column Sections
.inventory-section, .summary-section {
  margin-bottom: 24px;
}

.inventory-card, .summary-card {
  padding: 24px;
  background: white;
  border-radius: 16px;
  border: 2px solid #e2e8f0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);

  .inventory-header, .summary-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e2e8f0;

    mat-icon {
      color: #cc0000;
      font-size: 20px;
    }

    h4 {
      margin: 0;
      font-size: 18px;
      font-weight: 600;
      color: #1e293b;
    }
  }

  .inventory-content, .summary-content {
    .inventory-row, .summary-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;

      &.total, &.remaining {
        border-top: 1px solid #e2e8f0;
        margin-top: 8px;
        padding-top: 16px;
        font-weight: 600;
      }

      &.remaining {
        color: #059669;
      }

      .inventory-label, .summary-label {
        font-size: 14px;
        color: #64748b;
      }

      .inventory-value, .summary-value {
        font-size: 14px;
        font-weight: 500;
        color: #1e293b;
      }
    }
  }
}

.no-inventory-section {
  .no-inventory-card {
    padding: 40px;
    background: white;
    border-radius: 16px;
    border: 2px solid #e2e8f0;
    text-align: center;

    mat-icon {
      font-size: 48px;
      width: 48px;
      height: 48px;
      color: #94a3b8;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      font-size: 20px;
      font-weight: 600;
      color: #1e293b;
    }

    p {
      margin: 0;
      font-size: 14px;
      color: #64748b;
      line-height: 1.5;
    }
  }
}

// Action Buttons
.modal-actions {
  padding: 32px 40px;
  background: #f8fafc;
  border-top: 1px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
  gap: 16px;

  .cancel-btn {
    padding: 12px 24px;
    border: 2px solid #e2e8f0;
    color: #64748b;
    font-weight: 500;
    border-radius: 12px;
    transition: all 0.3s ease;

    &:hover {
      border-color: #cc0000;
      color: #cc0000;
      background: rgba(204, 0, 0, 0.05);
    }

    mat-icon {
      margin-right: 8px;
    }
  }

  .remove-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    font-weight: 600;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      background: linear-gradient(135deg, #b91c1c 0%, #991b1b 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
    }

    &:disabled {
      background: #94a3b8;
      box-shadow: none;
      cursor: not-allowed;
    }

    mat-icon {
      margin-right: 8px;
    }
  }
}

// Responsive Design - Much smaller content for mobile
@media (max-width: 768px) {
  .remove-coin-modal-container {
    width: 95vw;
    max-height: 90vh;
    transform: scale(0.9);
  }

  .content-layout {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .modal-header {
    padding: 0.75rem;

    .header-content {
      .header-text {
        h2 {
          font-size: 1rem;
        }

        p {
          font-size: 0.625rem;
        }
      }
    }
  }

  .modal-content {
    padding: 0.75rem;
  }

  .quantity-controls {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .series-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
  }

  .denomination-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.375rem;
  }

  .series-card, .denomination-card {
    padding: 0.375rem;
    border-radius: 4px;

    .series-icon, .denomination-icon {
      width: 20px;
      height: 20px;
      margin-bottom: 0.25rem;

      mat-icon {
        font-size: 0.75rem;
      }
    }

    .series-info, .denomination-info {
      h4 {
        font-size: 0.5rem;
        margin-bottom: 0.125rem;
      }

      p {
        font-size: 0.375rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .remove-coin-modal-container {
    width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }

  .content-layout {
    gap: 1rem;
  }

  .modal-header {
    padding: 1rem;

    .header-content {
      .header-text {
        h2 {
          font-size: 1.25rem;
        }

        p {
          font-size: 0.75rem;
        }
      }
    }
  }

  .modal-content {
    padding: 1rem;
  }

  .quantity-controls {
    gap: 0.75rem;
  }

  .series-grid, .denomination-grid {
    gap: 0.75rem;
  }
}

@media (max-width: 320px) {
  .modal-header {
    padding: 0.75rem;

    .header-content {
      .header-text {
        h2 {
          font-size: 1.125rem;
        }

        p {
          font-size: 0.625rem;
        }
      }
    }
  }

  .modal-content {
    padding: 0.75rem;
  }

  .quantity-controls {
    gap: 0.5rem;
  }

  .series-grid, .denomination-grid {
    gap: 0.5rem;
  }
}

// Global backdrop styles
:host ::ng-deep {
  .remove-coin-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
    transition: all 0.3s ease !important;

    // Add subtle hover effect to indicate interactivity
    &:hover {
      backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      -webkit-backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      background: rgba(15, 23, 42, 0.5) !important;
    }
  }

  .remove-coin-modal-panel {
    border-radius: 24px !important;
    overflow: hidden !important;
    box-shadow: 0 32px 64px rgba(0,0,0,0.12) !important;

    .mat-mdc-dialog-container {
      border-radius: 24px !important;
      overflow: hidden !important;
      padding: 0 !important;
    }
  }

  .modern-modal-panel {
    .mat-mdc-dialog-container {
      max-width: none !important;
      max-height: none !important;
    }
  }
}
