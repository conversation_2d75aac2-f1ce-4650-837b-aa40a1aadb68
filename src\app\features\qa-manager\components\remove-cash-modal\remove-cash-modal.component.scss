// Make the Material dialog container transparent
:host ::ng-deep .remove-cash-modal-panel .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}

:host ::ng-deep .modern-modal-panel .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
}

// Main Container
.remove-cash-modal-container {
  width: calc(100vw - 2rem);
  max-width: 1200px;
  margin: 0 1rem;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.95) 0%,
    rgba(248, 250, 252, 0.9) 100%);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 32px 64px rgba(0,0,0,0.12);
  overflow: hidden;
  animation: slideInUp 0.4s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

// Modal Header
.modal-header {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  color: white;
  padding: 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
    z-index: 1;
  }

  .header-content {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    z-index: 2;
    position: relative;

    .header-icon {
      width: 56px;
      height: 56px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      backdrop-filter: blur(10px);

      mat-icon {
        font-size: 2rem;
        width: 2rem;
        height: 2rem;
        color: white;
      }
    }

    .header-text {
      h2 {
        margin: 0;
        font-size: 1.75rem;
        font-weight: 700;
        letter-spacing: -0.025em;
      }

      p {
        margin: 0.25rem 0 0;
        opacity: 0.9;
        font-size: 1rem;
        font-weight: 400;
      }
    }
  }

  .close-button {
    z-index: 2;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(255, 255, 255, 0.2);
      transform: scale(1.05);
    }

    mat-icon {
      font-size: 1.25rem;
    }
  }
}

// Modal Content
.modal-content {
  padding: 2rem;
  max-height: 70vh;
  overflow-y: auto;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(148, 163, 184, 0.1);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(148, 163, 184, 0.3);
    border-radius: 3px;

    &:hover {
      background: rgba(148, 163, 184, 0.5);
    }
  }
}

// Two-column layout
.content-layout {
  display: grid;
  grid-template-columns: 1fr 400px;
  gap: 2rem;
  align-items: start;

  // Responsive design for smaller screens
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
}

.left-column {
  min-width: 0; // Prevents grid overflow
}

.right-column {
  position: sticky;
  top: 0;

  // On mobile, make it not sticky
  @media (max-width: 768px) {
    position: static;
  }

  .current-inventory-section,
  .summary-section,
  .no-inventory-section {
    margin-bottom: 1.5rem;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

// Form Sections
.form-section {
  margin-bottom: 2rem;
  animation: slideInUp 0.4s ease-out;

  &:nth-child(2) { animation-delay: 0.1s; }
  &:nth-child(3) { animation-delay: 0.2s; }
  &:nth-child(4) { animation-delay: 0.3s; }

  .section-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;

    .step-icon {
      color: #dc2626;
      font-size: 1.5rem;
      width: 1.5rem;
      height: 1.5rem;
    }

    h3 {
      color: var(--absa-dark-blue);
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0;
    }
  }
}

// Current Inventory Section
.current-inventory-section {
  .inventory-display {
    .inventory-card {
      background: rgba(239, 68, 68, 0.05);
      border: 2px solid rgba(239, 68, 68, 0.2);
      border-radius: 16px;
      padding: 1.5rem;

      .inventory-header {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        margin-bottom: 1rem;

        mat-icon {
          color: #dc2626;
          font-size: 1.25rem;
        }

        h4 {
          color: #dc2626;
          font-size: 1rem;
          font-weight: 600;
          margin: 0;
        }
      }

      .inventory-details {
        display: grid;
        gap: 0.75rem;

        .detail-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0;
          border-bottom: 1px solid rgba(239, 68, 68, 0.1);

          &:last-child {
            border-bottom: none;
          }

          &.total-value {
            font-weight: 600;
            color: #dc2626;
            border-top: 2px solid rgba(239, 68, 68, 0.2);
            padding-top: 0.75rem;
            margin-top: 0.5rem;
          }

          .detail-label {
            color: var(--absa-gray-dark);
            font-weight: 500;
          }

          .detail-value {
            color: var(--absa-dark-blue);
            font-weight: 600;
          }
        }
      }
    }
  }
}

// Series Grid
.series-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;

  .series-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 1rem;

    &:hover {
      border-color: #dc2626;
      box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
      background: rgba(255, 255, 255, 0.9);
      transform: translateY(-2px);
    }

    &.selected {
      border-color: #dc2626;
      background: rgba(220, 38, 38, 0.05);
      box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
    }

    .series-icon {
      width: 48px;
      height: 48px;
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;

      mat-icon {
        color: white;
        font-size: 1.5rem;
        width: 1.5rem;
        height: 1.5rem;
      }
    }

    .series-info {
      h4 {
        color: var(--absa-dark-blue);
        font-size: 1rem;
        font-weight: 600;
        margin: 0 0 0.25rem;
      }

      p {
        color: var(--absa-gray-medium);
        font-size: 0.875rem;
        margin: 0;
      }
    }
  }
}

// Quantity Controls
.quantity-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;

  .quantity-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;

    &:hover {
      border-color: #dc2626;
      box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
      background: rgba(255, 255, 255, 0.9);
    }

    .card-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;

      mat-icon {
        color: #dc2626;
        font-size: 1.25rem;
      }

      h4 {
        color: var(--absa-dark-blue);
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
        flex: 1;
      }

      .helper-text {
        color: var(--absa-gray-medium);
        font-size: 0.8rem;
        font-weight: 400;
      }
    }

    .input-container {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.75rem;

      .quantity-btn {
        width: 36px;
        height: 36px;
        min-width: 36px;
        background: rgba(148, 163, 184, 0.1);
        border: 1px solid rgba(148, 163, 184, 0.3);
        color: #dc2626;

        &:hover:not(:disabled) {
          background: #dc2626;
          color: white;
          border-color: #dc2626;
        }

        &:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }

        mat-icon {
          font-size: 1rem;
        }
      }

      .quantity-input {
        flex: 1;
        text-align: center;
        border: 2px solid rgba(226, 232, 240, 0.6);
        border-radius: 8px;
        padding: 0.75rem;
        font-size: 1rem;
        font-weight: 600;
        color: var(--absa-dark-blue);
        background: rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: #dc2626;
          box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
        }
      }
    }

    .quantity-info {
      .available {
        color: var(--absa-gray-medium);
        font-size: 0.8rem;
        font-weight: 500;
      }
    }
  }
}

// Summary Section
.summary-section {
  .summary-card {
    background: rgba(220, 38, 38, 0.05);
    border: 2px solid rgba(220, 38, 38, 0.2);
    border-radius: 16px;
    padding: 1.5rem;

    .summary-header {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      margin-bottom: 1rem;

      mat-icon {
        color: #dc2626;
        font-size: 1.25rem;
      }

      h4 {
        color: #dc2626;
        font-size: 1rem;
        font-weight: 600;
        margin: 0;
      }
    }

    .summary-content {
      .summary-row {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0.75rem 0;
        border-bottom: 1px solid rgba(220, 38, 38, 0.1);

        &:last-child {
          border-bottom: none;
        }

        &.remaining {
          border-top: 2px solid rgba(220, 38, 38, 0.2);
          margin-top: 0.5rem;
          padding-top: 1rem;
          font-weight: 600;
          color: #059669;
        }

        .summary-label {
          color: var(--absa-gray-dark);
          font-weight: 500;
        }

        .summary-value {
          color: var(--absa-dark-blue);
          font-weight: 600;
        }
      }
    }

    .breakdown-section {
      margin-top: 1.5rem;
      padding-top: 1.5rem;
      border-top: 1px solid rgba(220, 38, 38, 0.2);

      .breakdown-title {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        margin-bottom: 1rem;

        mat-icon {
          color: #dc2626;
          font-size: 1rem;
        }

        span {
          color: #dc2626;
          font-weight: 600;
          font-size: 0.875rem;
        }
      }

      .breakdown-items {
        display: grid;
        gap: 0.5rem;

        .breakdown-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 0.5rem 0.75rem;
          background: rgba(255, 255, 255, 0.5);
          border-radius: 8px;

          .breakdown-label {
            color: var(--absa-gray-dark);
            font-size: 0.875rem;
            font-weight: 500;
          }

          .breakdown-value {
            color: var(--absa-dark-blue);
            font-size: 0.875rem;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// No Inventory Section
.no-inventory-section {
  .no-inventory-card {
    background: rgba(239, 68, 68, 0.05);
    border: 2px solid rgba(239, 68, 68, 0.2);
    border-radius: 16px;
    padding: 2rem;
    text-align: center;

    mat-icon {
      font-size: 3rem;
      width: 3rem;
      height: 3rem;
      color: #dc2626;
      margin-bottom: 1rem;
    }

    h3 {
      color: #dc2626;
      font-size: 1.25rem;
      font-weight: 600;
      margin: 0 0 0.5rem;
    }

    p {
      color: var(--absa-gray-medium);
      font-size: 1rem;
      margin: 0;
    }
  }
}

// Reason Section
.reason-section {
  .reason-field {
    width: 100%;

    .mat-mdc-form-field-focus-overlay {
      background-color: rgba(220, 38, 38, 0.1);
    }

    .mat-mdc-text-field-wrapper {
      background: rgba(255, 255, 255, 0.8);
      backdrop-filter: blur(10px);
    }

    .mat-mdc-form-field-outline {
      color: rgba(226, 232, 240, 0.6);
    }

    &.mat-focused .mat-mdc-form-field-outline-thick {
      color: #dc2626;
    }

    .mat-mdc-form-field-label {
      color: var(--absa-gray-medium);
    }

    &.mat-focused .mat-mdc-form-field-label {
      color: #dc2626;
    }
  }
}

// Modal Actions
.modal-actions {
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg,
    rgba(248, 250, 252, 0.9) 0%,
    rgba(255, 255, 255, 0.8) 100%);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(226, 232, 240, 0.6);
  display: flex;
  justify-content: flex-end;
  gap: 1rem;

  .cancel-btn {
    border: 2px solid var(--absa-gray-medium);
    color: var(--absa-gray-dark);
    border-radius: 12px;
    padding: 0.75rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;

    &:hover {
      border-color: var(--absa-red);
      color: var(--absa-red);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(227, 24, 55, 0.2);
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }

  .remove-btn {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 12px rgba(220, 38, 38, 0.3);

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 8px 20px rgba(220, 38, 38, 0.4);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    mat-icon {
      margin-right: 0.5rem;
    }
  }
}

// Animations
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Global Modal Styles - Subtle backdrop blur that preserves background visibility
:host ::ng-deep {
  // Target all possible backdrop classes with maximum specificity
  .remove-cash-modal-backdrop,
  .cdk-overlay-backdrop.remove-cash-modal-backdrop,
  .mat-dialog-backdrop.remove-cash-modal-backdrop {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
    animation: backdropFadeIn 0.4s ease-out !important;
    transition: all 0.3s ease !important;
    cursor: pointer !important;

    // Add subtle hover effect to indicate interactivity
    &:hover {
      backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      -webkit-backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      background: rgba(15, 23, 42, 0.5) !important;
    }
  }

  .remove-cash-modal-panel {
    border-radius: 24px !important;
    overflow: hidden !important;
    box-shadow: 0 32px 64px rgba(0,0,0,0.12) !important;

    .mat-mdc-dialog-container {
      border-radius: 24px !important;
      overflow: hidden !important;
      padding: 0 !important;
    }
  }

  .modern-modal-panel {
    .mat-mdc-dialog-container {
      max-width: none !important;
      max-height: none !important;
    }
  }
}

@keyframes backdropFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px) !important;
    -webkit-backdrop-filter: blur(0px) !important;
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  }
}

// Denomination Grid
.denomination-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;

  // Responsive design for smaller screens
  @media (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }

  @media (max-width: 480px) {
    grid-template-columns: repeat(2, 1fr);
  }

  .denomination-card {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(226, 232, 240, 0.6);
    border-radius: 16px;
    padding: 1.25rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;

    &:hover {
      border-color: #dc2626;
      box-shadow: 0 4px 12px rgba(220, 38, 38, 0.1);
      background: rgba(255, 255, 255, 0.9);
      transform: translateY(-2px);
    }

    &.selected {
      border-color: #dc2626;
      background: rgba(220, 38, 38, 0.05);
      box-shadow: 0 4px 12px rgba(220, 38, 38, 0.2);
    }

    .denomination-icon {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
      border-radius: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0 auto 0.75rem;

      mat-icon {
        color: white;
        font-size: 1.25rem;
        width: 1.25rem;
        height: 1.25rem;
      }
    }

    .denomination-info {
      h4 {
        color: var(--absa-dark-blue);
        font-size: 0.875rem;
        font-weight: 600;
        margin: 0 0 0.25rem;
      }

      p {
        color: var(--absa-gray-medium);
        font-size: 0.75rem;
        margin: 0;
      }
    }
  }
}

// Additional Responsive Design - Much smaller content for mobile
@media (max-width: 768px) {
  .remove-cash-modal-container {
    width: 95vw;
    max-height: 90vh;
    transform: scale(0.9);
  }

  .modal-header {
    padding: 0.75rem;

    .header-content {
      gap: 0.5rem;

      .header-icon {
        width: 28px;
        height: 28px;
      }

      .header-text h2 {
        font-size: 1rem;
      }

      .header-text p {
        font-size: 0.625rem;
      }
    }
  }

  .modal-content {
    padding: 0.75rem;
  }

  .denomination-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 0.375rem;
  }

  .denomination-card {
    padding: 0.375rem;
    border-radius: 4px;

    .denomination-icon {
      width: 20px;
      height: 20px;
      margin-bottom: 0.25rem;

      mat-icon {
        font-size: 0.75rem;
        width: 0.75rem;
        height: 0.75rem;
      }
    }

    .denomination-info {
      h4 {
        font-size: 0.5rem;
        margin-bottom: 0.125rem;
      }

      p {
        font-size: 0.375rem;
      }
    }
  }
}

@media (max-width: 480px) {
  .remove-cash-modal-container {
    width: 100vw;
    max-height: 100vh;
    border-radius: 0;
  }

  .modal-header {
    padding: 1rem;

    .header-content {
      gap: 0.5rem;

      .header-icon {
        width: 32px;
        height: 32px;
      }

      .header-text h2 {
        font-size: 1.125rem;
      }

      .header-text p {
        font-size: 0.75rem;
      }
    }
  }

  .modal-content {
    padding: 1rem;
  }

  .denomination-card {
    padding: 0.75rem;

    .denomination-icon {
      width: 28px;
      height: 28px;
      margin-bottom: 0.375rem;

      mat-icon {
        font-size: 0.875rem;
        width: 0.875rem;
        height: 0.875rem;
      }
    }

    .denomination-info {
      h4 {
        font-size: 0.625rem;
      }

      p {
        font-size: 0.5rem;
      }
    }
  }
}

@media (max-width: 320px) {
  .modal-header {
    padding: 0.75rem;

    .header-content {
      .header-icon {
        width: 28px;
        height: 28px;
      }

      .header-text h2 {
        font-size: 1rem;
      }

      .header-text p {
        font-size: 0.625rem;
      }
    }
  }

  .modal-content {
    padding: 0.75rem;
  }

  .denomination-card {
    padding: 0.5rem;

    .denomination-icon {
      width: 24px;
      height: 24px;
      margin-bottom: 0.25rem;

      mat-icon {
        font-size: 0.75rem;
        width: 0.75rem;
        height: 0.75rem;
      }
    }

    .denomination-info {
      h4 {
        font-size: 0.5rem;
      }

      p {
        font-size: 0.375rem;
      }
    }
  }
}
