// Custom Theming for Angular Material
// For more information: https://material.angular.dev/guide/theming
@use '@angular/material' as mat;

html {
  @include mat.theme((color: (theme-type: light,
        primary: mat.$azure-palette,
        tertiary: mat.$blue-palette,
      ),
      typography: Roboto,
      density: 0,
    ));
}

/* You can add global styles to this file, and also import other style files */

html,
body {
  height: 100%;
}

body {
  margin: 0;
  font-family: Roboto, "Helvetica Neue", sans-serif;
  overflow-x: hidden; // Prevent horizontal scroll
}

// Ensure smooth scrolling and prevent layout shifts
* {
  box-sizing: border-box;
}

// Prevent any potential layout shifts from the fixed navbar
html {
  scroll-padding-top: 64px;
}

// Global Colors - Absa Brand Palette
:root {
  // Primary Absa Colors
  --absa-red: #911D2F;
  --absa-red-light: #B8253F;
  --absa-red-dark: #6B1522;
  --absa-red-50: rgba(145, 29, 47, 0.05);
  --absa-red-100: rgba(145, 29, 47, 0.1);
  --absa-red-200: rgba(145, 29, 47, 0.2);
  --absa-red-300: rgba(145, 29, 47, 0.3);

  // Complementary Colors
  --absa-gold: #D4AF37;
  --absa-gold-light: #E6C547;
  --absa-gold-dark: #B8941F;

  // Neutral Colors
  --absa-white: #FFFFFF;
  --absa-gray-50: #F8F9FA;
  --absa-gray-100: #F1F3F4;
  --absa-gray-200: #E8EAED;
  --absa-gray-300: #DADCE0;
  --absa-gray-400: #BDC1C6;
  --absa-gray-500: #9AA0A6;
  --absa-gray-600: #80868B;
  --absa-gray-700: #5F6368;
  --absa-gray-800: #3C4043;
  --absa-gray-900: #202124;

  // Status Colors
  --absa-success: #34A853;
  --absa-success-light: #4CAF50;
  --absa-warning: #FBBC04;
  --absa-warning-light: #FFC107;
  --absa-error: #EA4335;
  --absa-error-light: #F44336;
  --absa-info: #4285F4;
  --absa-info-light: #2196F3;

  // Gradients
  --absa-gradient-primary: linear-gradient(135deg, var(--absa-red) 0%, var(--absa-red-light) 100%);
  --absa-gradient-gold: linear-gradient(135deg, var(--absa-gold) 0%, var(--absa-gold-light) 100%);
  --absa-gradient-subtle: linear-gradient(135deg, var(--absa-gray-50) 0%, var(--absa-gray-100) 100%);

  // Shadows
  --absa-shadow-sm: 0 1px 3px rgba(145, 29, 47, 0.12), 0 1px 2px rgba(145, 29, 47, 0.24);
  --absa-shadow-md: 0 4px 6px rgba(145, 29, 47, 0.07), 0 2px 4px rgba(145, 29, 47, 0.06);
  --absa-shadow-lg: 0 10px 15px rgba(145, 29, 47, 0.1), 0 4px 6px rgba(145, 29, 47, 0.05);
  --absa-shadow-xl: 0 20px 25px rgba(145, 29, 47, 0.1), 0 10px 10px rgba(145, 29, 47, 0.04);

  // Legacy support
  --backgound-white: var(--absa-white);
  --quick-actions-bg: var(--absa-gray-200);
}

// Custom Snackbar Styles
.success-snackbar {
  background-color: #4caf50 !important;
  color: white !important;

  .mat-mdc-snack-bar-label {
    color: white !important;
  }

  .mat-mdc-button {
    color: white !important;
  }
}

// Tab styles removed - ready for redesign

// Global Modal Dialog Fixes
.add-cash-modal-panel .mat-mdc-dialog-container,
.add-coin-modal-panel .mat-mdc-dialog-container,
.remove-cash-modal-panel .mat-mdc-dialog-container,
.remove-coin-modal-panel .mat-mdc-dialog-container,
.modern-modal-panel .mat-mdc-dialog-container {
  background: transparent !important;
  box-shadow: none !important;
  padding: 0 !important;
  max-width: none !important;
  width: 100% !important;
}

// Global Backdrop Blur Styles - Subtle blur that preserves background visibility
// Target all possible backdrop implementations
.cdk-overlay-backdrop.add-cash-modal-backdrop,
.cdk-overlay-backdrop.blur-backdrop,
.mat-dialog-backdrop.add-cash-modal-backdrop,
.mat-dialog-backdrop.blur-backdrop,
.add-cash-modal-backdrop,
.blur-backdrop,
.cdk-overlay-backdrop-showing.add-cash-modal-backdrop,
.cdk-overlay-backdrop-showing.blur-backdrop,
.cdk-overlay-container .add-cash-modal-backdrop,
.cdk-overlay-container .blur-backdrop,
.cdk-overlay-pane .add-cash-modal-backdrop,
.cdk-overlay-pane .blur-backdrop {
  backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  background: rgba(15, 23, 42, 0.4) !important;
  animation: backdropBlurFadeIn 0.4s ease-out !important;
  transition: all 0.3s ease !important;
  cursor: pointer !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  z-index: 1000 !important;
}

// Hover effects - subtle increase in blur
.cdk-overlay-backdrop.add-cash-modal-backdrop:hover,
.cdk-overlay-backdrop.remove-cash-modal-backdrop:hover,
.cdk-overlay-backdrop.blur-backdrop:hover,
.mat-dialog-backdrop.add-cash-modal-backdrop:hover,
.mat-dialog-backdrop.remove-cash-modal-backdrop:hover,
.mat-dialog-backdrop.blur-backdrop:hover,
.add-cash-modal-backdrop:hover,
.remove-cash-modal-backdrop:hover,
.blur-backdrop:hover,
.cdk-overlay-backdrop-showing.add-cash-modal-backdrop:hover,
.cdk-overlay-backdrop-showing.remove-cash-modal-backdrop:hover,
.cdk-overlay-backdrop-showing.blur-backdrop:hover,
.cdk-overlay-container .add-cash-modal-backdrop:hover,
.cdk-overlay-container .remove-cash-modal-backdrop:hover,
.cdk-overlay-container .blur-backdrop:hover,
.cdk-overlay-pane .add-cash-modal-backdrop:hover,
.cdk-overlay-pane .remove-cash-modal-backdrop:hover,
.cdk-overlay-pane .blur-backdrop:hover {
  backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
  -webkit-backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
  background: rgba(15, 23, 42, 0.5) !important;
}

// Fallback: Target overlay container when modal is open
.cdk-overlay-container:has(.add-cash-modal-panel) .cdk-overlay-backdrop,
.cdk-overlay-container:has(.remove-cash-modal-panel) .cdk-overlay-backdrop,
.cdk-overlay-container:has(.modern-modal-panel) .cdk-overlay-backdrop {
  backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  background: rgba(15, 23, 42, 0.4) !important;
}

// Alternative fallback for browsers that don't support :has()
.cdk-overlay-backdrop {
  &[class*="add-cash-modal"],
  &[class*="remove-cash-modal"] {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
  }

  // Programmatically applied blur
  &.blur-backdrop-applied {
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    background: rgba(15, 23, 42, 0.4) !important;
    transition: all 0.3s ease !important;

    &:hover {
      backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      -webkit-backdrop-filter: blur(12px) saturate(1.2) brightness(0.85) !important;
      background: rgba(15, 23, 42, 0.5) !important;
    }
  }
}

// Nuclear option: Apply blur to ANY backdrop when our modal classes are present
body:has(.add-cash-modal-panel) .cdk-overlay-backdrop,
body:has(.remove-cash-modal-panel) .cdk-overlay-backdrop,
body:has(.modern-modal-panel) .cdk-overlay-backdrop {
  backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  background: rgba(15, 23, 42, 0.4) !important;
}

// Backdrop animation - subtle blur fade in
@keyframes backdropBlurFadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px) !important;
    -webkit-backdrop-filter: blur(0px) !important;
  }
  to {
    opacity: 1;
    backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
    -webkit-backdrop-filter: blur(8px) saturate(1.1) brightness(0.9) !important;
  }
}


