<div class="remove-coin-modal-container">
  <!-- Modern Header with Gradient -->
  <div class="modal-header">
    <div class="header-content">
      <div class="header-icon">
        <mat-icon>money_off</mat-icon>
      </div>
      <div class="header-text">
        <h2>Remove Coin Inventory</h2>
        <p>Manage your coin reserves with precision</p>
      </div>
    </div>
    <button mat-icon-button class="close-button" (click)="onCancel()">
      <mat-icon>close</mat-icon>
    </button>
  </div>

  <!-- Main Content Area -->
  <div class="modal-content">
    <div class="content-layout">
      <div class="left-column">
        <form #removeCoinForm="ngForm" class="remove-coin-form">
          <!-- Step 1: Series Selection -->
          <div class="form-section series-section">
            <div class="section-header">
              <mat-icon class="step-icon">category</mat-icon>
              <h3>Choose Series</h3>
            </div>
            <div class="series-grid">
              <div class="series-card"
                   *ngFor="let series of availableSeries"
                   [class.selected]="selectedSeries === series"
                   (click)="selectedSeries = series; onSeriesChange()">
                <div class="series-icon">
                  <mat-icon>{{ getSeriesIcon(series) }}</mat-icon>
                </div>
                <div class="series-info">
                  <h4>{{ COIN_SERIES_LABELS[series] }}</h4>
                  <p>{{ getSeriesDescription(series) }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 2: Denomination Selection -->
          <div class="form-section denomination-section" *ngIf="selectedSeries">
            <div class="section-header">
              <mat-icon class="step-icon">payments</mat-icon>
              <h3>Choose Denomination</h3>
            </div>
            <div class="denomination-grid">
              <div class="denomination-card"
                   *ngFor="let denomination of availableDenominations"
                   [class.selected]="selectedDenomination === denomination"
                   (click)="selectedDenomination = denomination; onDenominationChange()">
                <div class="denomination-icon">
                  <mat-icon>{{ +denomination >= 1 ? 'account_balance_wallet' : 'monetization_on' }}</mat-icon>
                </div>
                <div class="denomination-info">
                  <h4>{{ COIN_DENOMINATION_LABELS[denomination] }}</h4>
                  <p>{{ +denomination >= 1 ? 'High value coin' : 'Standard coin' }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 3: Quantity Input -->
          <div class="form-section quantity-section" *ngIf="selectedDenomination && currentQuantity > 0">
            <div class="section-header">
              <mat-icon class="step-icon">calculate</mat-icon>
              <h3>Specify Quantity</h3>
            </div>
            <div class="quantity-controls">
              <div class="quantity-card batches-card">
                <div class="card-header">
                  <mat-icon>inventory_2</mat-icon>
                  <h4>Batches</h4>
                  <span class="helper-text">{{ getCoinsPerBatch() }} coins each</span>
                </div>
                <div class="input-container">
                  <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(-1)" [disabled]="batches <= 0">
                    <mat-icon>remove</mat-icon>
                  </button>
                  <input type="number"
                         [(ngModel)]="batches"
                         name="batches"
                         min="0"
                         [max]="currentBatches"
                         class="quantity-input"
                         (input)="onQuantityChange()">
                  <button type="button" mat-icon-button class="quantity-btn" (click)="adjustBatches(1)" [disabled]="batches >= currentBatches">
                    <mat-icon>add</mat-icon>
                  </button>
                </div>
                <div class="quantity-info">
                  <span class="available">Available: {{ currentBatches }} batches</span>
                  <span class="value">Value per batch: {{ formatCurrency(getBatchValue()) }}</span>
                </div>
              </div>

              <div class="quantity-card singles-card">
                <div class="card-header">
                  <mat-icon>receipt_long</mat-icon>
                  <h4>Singles</h4>
                  <span class="helper-text">Individual coins</span>
                </div>
                <div class="input-container">
                  <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(-1)" [disabled]="singles <= 0">
                    <mat-icon>remove</mat-icon>
                  </button>
                  <input type="number"
                         [(ngModel)]="singles"
                         name="singles"
                         min="0"
                         [max]="currentQuantity - (batches * getCoinsPerBatch())"
                         class="quantity-input"
                         (input)="onQuantityChange()">
                  <button type="button" mat-icon-button class="quantity-btn" (click)="adjustSingles(1)" [disabled]="singles >= (currentQuantity - (batches * getCoinsPerBatch()))">
                    <mat-icon>add</mat-icon>
                  </button>
                </div>
                <div class="quantity-info">
                  <span class="available">Available: {{ currentQuantity - (batches * getCoinsPerBatch()) }} singles</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 4: Reason (Optional) -->
          <div class="form-section reason-section" *ngIf="selectedDenomination && currentQuantity > 0">
            <div class="section-header">
              <mat-icon class="step-icon">description</mat-icon>
              <h3>Reason (Optional)</h3>
            </div>
            <mat-form-field class="reason-field" appearance="outline">
              <mat-label>Reason for removal</mat-label>
              <input matInput
                     [(ngModel)]="reason"
                     name="reason"
                     placeholder="e.g., Damaged coins, End of day reconciliation"
                     maxlength="200">
              <mat-hint>Optional: Provide a reason for this removal</mat-hint>
            </mat-form-field>
          </div>
        </form>
      </div>

      <div class="right-column">
        <!-- Current Inventory Display -->
        <div class="inventory-section" *ngIf="selectedDenomination && currentQuantity > 0">
          <div class="inventory-card">
            <div class="inventory-header">
              <mat-icon>inventory</mat-icon>
              <h4>Current Inventory</h4>
            </div>
            <div class="inventory-content">
              <div class="inventory-row">
                <span class="inventory-label">Total Coins:</span>
                <span class="inventory-value">{{ currentQuantity }}</span>
              </div>
              <div class="inventory-row">
                <span class="inventory-label">Batches:</span>
                <span class="inventory-value">{{ currentBatches }} ({{ currentBatches * getCoinsPerBatch() }} coins)</span>
              </div>
              <div class="inventory-row">
                <span class="inventory-label">Singles:</span>
                <span class="inventory-value">{{ currentSingles }}</span>
              </div>
              <div class="inventory-row total">
                <span class="inventory-label">Total Value:</span>
                <span class="inventory-value">{{ formatCurrency(currentQuantity * selectedDenomination) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Summary Section -->
        <div class="summary-section" *ngIf="totalQuantity > 0 && selectedDenomination">
          <div class="summary-card">
            <div class="summary-header">
              <mat-icon>summarize</mat-icon>
              <h4>Removal Summary</h4>
            </div>
            <div class="summary-content">
              <div class="summary-row">
                <span class="summary-label">Removing:</span>
                <span class="summary-value">{{ totalQuantity }} coins ({{ formatCurrency(totalValue) }})</span>
              </div>
              <div class="summary-row remaining">
                <span class="summary-label">Remaining:</span>
                <span class="summary-value">{{ remainingQuantity }} coins ({{ formatCurrency(remainingQuantity * selectedDenomination) }})</span>
              </div>
            </div>
          </div>
        </div>

        <!-- No Inventory Message -->
        <div class="no-inventory-section" *ngIf="selectedDenomination && currentQuantity === 0">
          <div class="no-inventory-card">
            <mat-icon>inventory_2</mat-icon>
            <h3>No Inventory Available</h3>
            <p>There are currently no {{ getSelectedDenominationLabel() }} coins available for removal.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Action Buttons -->
  <div class="modal-actions">
    <button mat-stroked-button class="cancel-btn" (click)="onCancel()">
      <mat-icon>cancel</mat-icon>
      Cancel
    </button>
    <button mat-raised-button
            class="remove-btn"
            [disabled]="!isFormValid()"
            (click)="onRemoveCoins()">
      <mat-icon>remove_circle</mat-icon>
      Remove from Inventory
    </button>
  </div>
</div>
