// ===== RESPONSIVE BREAKPOINTS =====
$mobile-small: 320px;
$mobile: 480px;
$tablet: 768px;
$desktop: 1024px;
$desktop-large: 1200px;
$desktop-xl: 1440px;

// ===== RESPONSIVE MIXINS =====
@mixin mobile-small {
  @media (max-width: #{$mobile-small - 1px}) {
    @content;
  }
}

@mixin mobile {
  @media (max-width: #{$mobile - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (max-width: #{$tablet - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: $desktop) {
    @content;
  }
}

@mixin desktop-large {
  @media (min-width: $desktop-large) {
    @content;
  }
}

// ===== DASHBOARD CONTAINER =====
.dashboard-container {
  padding: 2rem;
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);

  @include tablet {
    padding: 1.5rem;
  }

  @include mobile {
    padding: 1rem;
    // Scale down everything on mobile for better fit
    transform: scale(0.9);
    transform-origin: top left;
    width: 111.11%; // Compensate for scale
  }

  @include mobile-small {
    padding: 0.75rem;
    transform: scale(0.8);
    width: 125%; // Compensate for scale
  }

  h2 {
    margin-bottom: 2rem;
    color: #1e293b;
    font-size: 2.5rem;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;

    @include tablet {
      font-size: 2rem;
      margin-bottom: 1.5rem;
    }

    @include mobile {
      font-size: 1.75rem;
      margin-bottom: 1rem;
    }

    @include mobile-small {
      font-size: 1.5rem;
      margin-bottom: 0.75rem;
    }
  }

  .content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 2rem;
    border-radius: 16px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;

    @include tablet {
      padding: 1.5rem;
      border-radius: 12px;
    }

    @include mobile {
      padding: 1rem;
      border-radius: 8px;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    }

    @include mobile-small {
      padding: 0.75rem;
      border-radius: 6px;
    }

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

      @include mobile {
        transform: none;
        box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
      }
    }
  }
}
